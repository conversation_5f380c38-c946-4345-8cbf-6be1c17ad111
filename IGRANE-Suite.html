<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IGRANE Deluxe Suite | Atfel Kasbah Hotel | Ourika Valley</title>
    <meta name="description"
        content="Discover the IGRANE Deluxe Suite at Atfel Kasbah. Experience unparalleled luxury with panoramic Atlas Mountains views from your private balcony. Book your exclusive Moroccan retreat in the Ourika Valley.">
    <link rel="canonical" href="https://www.atfelkasbah.com/ayour-deluxe-suite.html">

    <link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css" />
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="hamburger-menu-fix.css">

    <!-- Fallback for when JavaScript is disabled -->
    <noscript>
        <style>
            .mobile-nav-toggle {
                display: none;
            }

            .nav-links {
                display: block !important;
                position: static !important;
                transform: none !important;
                background: transparent !important;
                width: 100% !important;
                pointer-events: auto !important;
            }
        </style>
    </noscript>
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #f7f5f2;
            --text-dark: #2a2a2a;
            --text-light: #6b6b6b;
            --white: #ffffff;
            --border-color: #e5e0da;
        }

        /* Lightbox styles */
        .lightbox-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .lightbox-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .lightbox-container {
            position: relative;
            width: 90%;
            height: 90%;
            max-width: 1200px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .lightbox-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .lightbox-close {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.2s;
            z-index: 2010;
        }

        .lightbox-close:hover {
            background-color: rgba(255, 255, 255, 0.5);
        }

        .lightbox-close svg {
            width: 24px;
            height: 24px;
            color: white;
        }

        .lightbox-nav {
            position: absolute;
            top: 50%;
            width: 100%;
            display: flex;
            justify-content: space-between;
            transform: translateY(-50%);
            z-index: 2005;
        }

        .lightbox-nav-btn {
            width: 50px;
            height: 50px;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.2s;
            margin: 0 20px;
        }

        .lightbox-nav-btn:hover {
            background-color: rgba(255, 255, 255, 0.5);
        }

        .lightbox-nav-btn svg {
            width: 24px;
            height: 24px;
            color: white;
        }

        .gallery-main .swiper-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            cursor: pointer;
            /* Add pointer cursor to indicate clickable */
        }

        body {
            background-color: var(--white);
        }

        body.modal-open {
            overflow: hidden;
        }

        #header {
            background-color: var(--white);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.07);
        }

        .room-detail-hero {
            padding-top: 100px;
            padding-bottom: 60px;
        }

        .room-gallery {
            margin-bottom: 40px;
            position: relative;
        }

        .gallery-main {
            width: 100%;
            height: 500px;
            margin-bottom: 1rem;
            border-radius: 12px;
        }

        .gallery-main .swiper-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .gallery-thumbs {
            height: 100px;
            box-sizing: border-box;
            padding: 10px 0;
        }

        .gallery-thumbs .swiper-slide {
            width: 25%;
            height: 100%;
            opacity: 0.5;
            transition: opacity 0.3s ease;
            cursor: pointer;
        }

        .gallery-thumbs .swiper-slide:hover {
            opacity: 1;
        }

        .gallery-thumbs .swiper-slide-thumb-active {
            opacity: 1;
        }

        .gallery-thumbs .swiper-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
        }

        .swiper-button-next,
        .swiper-button-prev {
            color: var(--white);
            background-color: rgba(0, 0, 0, 0.3);
            width: 44px;
            height: 44px;
            border-radius: 50%;
        }

        .swiper-button-next:hover,
        .swiper-button-prev:hover {
            background-color: rgba(0, 0, 0, 0.5);
        }

        .swiper-button-next::after,
        .swiper-button-prev::after {
            font-size: 1.2rem;
            font-weight: 800;
        }

        .room-detail-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 60px;
            align-items: flex-start;
        }

        .room-main-info h1 {
            font-size: 3rem;
            margin-bottom: 0.5rem;
        }

        .room-main-info .subtitle {
            font-size: 1.5rem;
            color: var(--text-light);
            margin-bottom: 2rem;
            font-weight: 300;
        }

        .room-main-info .description {
            font-size: 1.1rem;
            line-height: 1.7;
            color: var(--text-light);
        }

        .room-main-info .section-divider {
            border: 0;
            height: 1px;
            background: var(--border-color);
            margin: 2.5rem 0;
        }

        .room-main-info h2 {
            font-size: 1.75rem;
            margin-bottom: 2rem;
        }

        /* Room layout info styling */
        .room-layout-info {
            margin-bottom: 2rem;
            border-radius: 10px;
            background-color: var(--secondary-color);
            padding: 1.5rem;
        }

        .layout-item {
            margin-bottom: 1rem;
        }

        .layout-item:last-child {
            margin-bottom: 0;
        }

        .layout-item h3 {
            display: flex;
            align-items: center;
            font-size: 1.2rem;
            font-weight: 500;
            color: var(--text-dark);
            margin: 0;
        }

        .layout-item h3 svg {
            width: 24px;
            height: 24px;
            margin-left: 10px;
            color: var(--primary-color);
        }

        .amenities-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem 2rem;
            list-style: none;
            padding: 0;
        }

        .amenity-item {
            display: flex;
            align-items: center;
            gap: 15px;
            transition: transform 0.2s ease;
        }

        .amenity-item:hover {
            transform: translateY(-3px);
        }

        .amenity-item svg {
            width: 24px;
            height: 24px;
            color: var(--primary-color);
            flex-shrink: 0;
        }

        .amenity-item span {
            font-size: 1rem;
            color: var(--text-light);
        }

        .booking-panel {
            position: sticky;
            top: 120px;
            padding: 2rem;
            background: var(--white);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        }

        .booking-panel-price {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .booking-panel-price .per-night {
            font-size: 1rem;
            color: var(--text-light);
            margin-left: 8px;
        }

        .booking-panel .price-info {
            font-size: 0.95rem;
            color: var(--text-light);
            margin-top: 0.5rem;
        }

        .booking-panel .btn-primary {
            display: block;
            width: 100%;
            text-align: center;
            padding: 15px;
            margin-top: 1.5rem;
            font-size: 1.2rem;
        }

        /* --- Booking Modal Styles --- */
        .booking-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(30, 20, 10, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .booking-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .booking-modal {
            background-color: var(--white);
            padding: 2.5rem 3rem;
            border-radius: 16px;
            box-shadow: 0 10px 45px rgba(0, 0, 0, 0.15);
            width: 90%;
            max-width: 550px;
            text-align: center;
            position: relative;
            transform: scale(0.95);
            transition: transform 0.3s ease;
        }

        .booking-overlay.active .booking-modal {
            transform: scale(1);
        }

        .booking-modal .modal-close-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: none;
            border: none;
            font-size: 2.5rem;
            font-weight: 300;
            line-height: 1;
            color: #aaa;
            cursor: pointer;
            transition: color 0.2s;
        }

        .booking-modal .modal-close-btn:hover {
            color: var(--text-dark);
        }

        .booking-modal h3 {
            font-size: 1.5rem;
            color: var(--text-dark);
            margin-top: 0;
            margin-bottom: 1.5rem;
            font-weight: 500;
        }

        .booking-modal .recommended-text {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 0.9em;
        }

        .booking-modal .partner-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-direction: column;
        }

        .booking-modal .partner-btn {
            flex: 1;
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: all 0.2s ease-in-out;
            text-decoration: none;
        }

        .booking-modal .partner-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border-color: #ccc;
        }

        .booking-modal .partner-btn img {
            max-height: 25px;
            width: auto;
            max-width: 120px;
        }

        .booking-modal .direct-booking-buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .direct-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 14px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-size: 1.1rem;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
        }

        .direct-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .direct-btn svg {
            width: 22px;
            height: 22px;
        }

        .direct-btn.whatsapp-btn {
            background-color: #25D366;
            color: var(--white);
        }

        .direct-btn.call-btn {
            background-color: var(--text-dark);
            color: var(--white);
        }

        @media (max-width: 992px) {
            .room-detail-content {
                grid-template-columns: 1fr;
            }

            .booking-panel {
                position: static;
                margin-top: 2rem;
            }
        }

        @media (max-width: 768px) {
            .room-main-info h1 {
                font-size: 2.5rem;
            }

            .amenities-grid {
                grid-template-columns: 1fr;
            }

            .gallery-main {
                height: 320px;
            }

            .gallery-thumbs {
                height: 80px;
            }

            .booking-modal {
                padding: 2rem 1.5rem;
            }
        }
    </style>

    <!-- *** SEO: JSON-LD Schema Markup for Hotel Room *** -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "HotelRoom",
      "name": "IGRANE Suite - Deluxe Suite",
      "description": "The IGRANE Suite is a premier deluxe suite at Atfel Kasbah, offering breathtaking panoramic views of the Atlas Mountains from a private balcony. This suite combines authentic Moroccan elegance with modern luxury, featuring a king-sized bed, air conditioning, and premium amenities for an unforgettable stay in the Ourika Valley.",
      "image": "https://www.atfelkasbah.com/IGRANE-Suite/Best-luxury-travel-hotel-Kasbah-atfel-ourika-in-morocco-summer-Mounatinview-hotel3.jpg",
      "bed": {
        "@type": "BedDetails",
        "typeOfBed": "King",
        "numberOfBeds": 1
      },
      "occupancy": {
        "@type": "QuantitativeValue",
        "maxValue": 2,
        "unitText": "adults"
      },
       "amenityFeature": [
        { "@type": "LocationFeatureSpecification", "name": "Mountain View", "value": true },
        { "@type": "LocationFeatureSpecification", "name": "Garden View", "value": true },
        { "@type": "LocationFeatureSpecification", "name": "Private Balcony", "value": true },
        { "@type": "LocationFeatureSpecification", "name": "Private Entrance", "value": true },
        { "@type": "LocationFeatureSpecification", "name": "Air Conditioning", "value": true },
        { "@type": "LocationFeatureSpecification", "name": "Complimentary Breakfast", "value": true },
        { "@type": "LocationFeatureSpecification", "name": "Free WiFi", "value": true },
        { "@type": "LocationFeatureSpecification", "name": "Anti-allergy room", "value": true },
        { "@type": "LocationFeatureSpecification", "name": "Dining area", "value": true },
        { "@type": "LocationFeatureSpecification", "name": "Wardrobe or closet", "value": true },
        { "@type": "LocationFeatureSpecification", "name": "Board games/puzzles", "value": true },
        { "@type": "LocationFeatureSpecification", "name": "Bathrobe", "value": true },
        { "@type": "LocationFeatureSpecification", "name": "Free toiletries", "value": true },
        { "@type": "LocationFeatureSpecification", "name": "Hair dryer", "value": true }
      ],
      "floorSize": { "@type": "QuantitativeValue", "value": 60, "unitCode": "MTK" },
      "offers": { "@type": "Offer", "price": "139", "priceCurrency": "USD", "description": "Price per night. Only a few suites left - book now before it’s gone!" },
      "partOfLodgingBusiness": {
        "@type": "Hotel",
        "name": "Atfel Kasbah",
        "address": { "@type": "PostalAddress", "streetAddress": "Ourika Valley", "addressLocality": "Ourika", "addressRegion": "Marrakesh-Safi", "postalCode": "42452", "addressCountry": "MA" },
        "telephone": "+212-XXX-XXXXXX",
        "url": "https://www.atfelkasbah.com/"
      }
    }
    </script>
</head>

<body>

    <header id="header">
        <nav class="container">
            <a href="index.html" class="logo">Atfel Kasbah</a>
            <button class="mobile-nav-toggle" aria-controls="primary-navigation" aria-expanded="false"><span
                    class="line line-top"></span><span class="line line-middle"></span><span
                    class="line line-bottom"></span></button>
            <ul id="primary-navigation" data-visible="false" class="nav-links">
                <li><a href="index.html#rooms">Rooms</a></li>
                <li><a href="gallery.html">Gallery</a></li>
                <li class="cta-item"><a href="index.html#rooms" class="cta-button">Book now</a></li>
                <li><a href="aboutus.html">About Us</a></li>
                <li><a href="contactus.html">Contact</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section class="room-detail-hero">
            <div class="container">
                <div class="room-gallery">
                    <div class="swiper gallery-main">
                        <div class="swiper-wrapper"></div>
                        <div class="swiper-button-next"></div>
                        <div class="swiper-button-prev"></div>
                    </div>
                    <div thumbsSlider="" class="swiper gallery-thumbs">
                        <div class="swiper-wrapper"></div>
                    </div>
                </div>

                <div class="room-detail-content">
                    <div class="room-main-info">
                        <h1>IGRANE Suite</h1>
                        <p class="subtitle">Deluxe suite</p>
                        <p class="description">Discover <strong>Suite IGRANE</strong> at <strong>Kasbah Atfel</strong>, a spacious retreat featuring a plush king‑size bed and soothing pistachio‑green décor. Enjoy <strong>garden and pool views</strong> from your private balcony as you unwind in complete comfort—air conditioning ensures an ideal temperature even on warm summer days. Perfect for couples or solo travelers seeking a refreshing escape, this suite combines luxury amenities with tranquil surroundings for an unforgettable stay. Ideal for searches like “Marrakech suite with pool view,” “garden view hotel room Ourika,” or “relaxing kasbah suite.”</p>
                            <hr class="section-divider">

                            <h2>Room Layout</h2>
                            <div class="room-layout-info">
                                <div class="layout-item">
                                    <h3>1 king bed </h3>
                                </div>
                                
                            </div>
    
                            <h2>In your private bathroom:</h2>
                            <div class="amenities-grid">
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                                        stroke-linecap="round" stroke-linejoin="round">
                                        <!-- Soap Dispenser Bottle -->
                                        <path d="M4 21V9a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
                                        <path d="M4 12h8" />
    
                                        <!-- Dispenser Pump -->
                                        <path d="M8 7V4a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1v3" />
                                        <path d="M12 7H8" />
    
                                        <!-- Soap Bar -->
                                        <path
                                            d="M21 21a2 2 0 0 1-2 2H11a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v4z" />
                                        <path d="M12 18h.01" />
                                        <path d="M17 18h.01" />
    
                                        <!-- Bubbles -->
                                        <path d="M19 5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z" />
                                        <path d="M21 8a2 2 0 1 1 0-4 2 2 0 0 1 0 4z" />
                                        <path d="M16 9a0.5 0.5 0 1 1 0-1 .5 .5 0 0 1 0 1z" />
                                    </svg><span>Free toiletries</span>
                                </div>
                                <div class="amenity-item">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                        stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M17.25 3h-3.18a.75.75 0 00-.74.56l-.57 2.06-1.01-1.01a.75.75 0 00-1.06 0l-1.01 1.01-.57-2.06A.75.75 0 009.93 3H6.75A2.25 2.25 0 004.5 5.25V9h15V5.25A2.25 2.25 0 0017.25 3z" />
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M4.5 9v9.75c0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75V12h3v7.5a.75.75 0 00.75.75h4.5a.75.75 0 00.75-.75V9" />
                                    </svg>
                                    <span>Bathrobe</span>
                                </div>
                                <div class="amenity-item">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                        stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M7 16v2a3 3 0 0 0 6 0v-2" />
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M10 12h2m0 0v-5h-2v5" />
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M5 11h14a2 2 0 0 1 2 2v4a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-4a2 2 0 0 1 2-2Z" />
                                    </svg>
                                    <span>Toilet</span>
                                </div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 00-3 3h15.75m-12.75-3h11.218c.51 0 .962-.344 1.087-.836l.383-1.437M7.5 14.25V5.25A2.25 2.25 0 019.75 3h4.5A2.25 2.25 0 0116.5 5.25v9" />
                                    </svg><span>Bathtub or shower</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M3.75 3v11.25A2.25 2.25 0 006 16.5h12A2.25 2.25 0 0020.25 14.25V3M3.75 3H20.25M3.75 3h.008v.008H3.75V3zm16.5 0h.008v.008H20.25V3zM3.75 6h.008v.008H3.75V6zm16.5 0h.008v.008H20.25V6zM3.75 9h.008v.008H3.75V9zm16.5 0h.008v.008H20.25V9z" />
                                    </svg><span>Towels</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M12 6.75a5.25 5.25 0 015.25 5.25H6.75a5.25 5.25 0 015.25-5.25zm0 0V3m0 3.75c-3.14 0-6.18.52-9 1.5m9-1.5c3.14 0 6.18.52 9 1.5m-9 0v9m0-9H3m9 0h9" />
                                    </svg><span>Hair dryer</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M15.75 21v-4.875a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 019.75 12V6.75a1.125 1.125 0 011.125-1.125h3.375c1.862 0 3.375 1.513 3.375 3.375v4.875" />
                                    </svg><span>Toilet paper</span></div>
                            </div>
    
                            <hr class="section-divider">
    
                            <h2>View:</h2>
                            <div class="amenities-grid">
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M19.5 12c0-5.13-4.17-9.3-9.3-9.3S.9 6.87.9 12c0 2.1.7 4.07 1.87 5.64l-1.5 1.5a.75.75 0 001.06 1.06l1.5-1.5A9.26 9.26 0 0010.2 21.3c5.13 0 9.3-4.17 9.3-9.3zm-9.3 7.5c-4.14 0-7.5-3.36-7.5-7.5S6.06 4.5 10.2 4.5s7.5 3.36 7.5 7.5-3.36 7.5-7.5 7.5z" />
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M10.2 15.75a3.75 3.75 0 100-7.5 3.75 3.75 0 000 7.5z" />
                                    </svg><span>Garden view</span>
                                </div>
                                
                            </div>
    
                            <hr class="section-divider">
    
                            <h2>Facilities:</h2>
                            <div class="amenities-grid">
                                
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z" />
                                    </svg><span>Air conditioning</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                                    </svg><span>Safe</span></div>
                                
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M3.75 3v11.25A2.25 2.25 0 006 16.5h12A2.25 2.25 0 0020.25 14.25V3M3.75 3H20.25M3.75 3h.008v.008H3.75V3zm16.5 0h.008v.008H20.25V3zM3.75 6h.008v.008H3.75V6zm16.5 0h.008v.008H20.25V6zM3.75 9h.008v.008H3.75V9zm16.5 0h.008v.008H20.25V9z" />
                                    </svg><span>Linens</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M5.636 5.636a9 9 0 1012.728 0M12 3v9" />
                                    </svg><span>Socket near the bed</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M9.816 6.336a3 3 0 014.368 0l2.364 2.364a3 3 0 010 4.368L10.364 19.5a3 3 0 01-4.368-4.368l6.336-6.336" />
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M12.75 12.75l-2.434-2.434" />
                                    </svg><span>Hypoallergenic</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z" />
                                    </svg><span>Tile/Marble floor</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75" />
                                    </svg><span>Desk</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M6.75 7.5l3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0021 18V6a2.25 2.25 0 00-2.25-2.25H5.25A2.25 2.25 0 003 6v12a2.25 2.25 0 002.25 2.25z" />
                                    </svg><span>Sitting area</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M15.75 5.25a3 3 0 013 3m3 0a6 6 0 01-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1121.75 8.25z" />
                                    </svg><span>Private entrance</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M6 20.25h12m-7.5-3v3m3-3v3m-10.125-3h17.25c.621 0 1.125-.504 1.125-1.125V4.875c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125z" />
                                    </svg><span>TV</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M8.288 15.038a5.25 5.25 0 017.424 0M5.106 11.856c3.807-3.808 9.98-3.808 13.788 0M1.924 8.674c5.565-5.565 14.587-5.565 20.152 0M12.53 18.22l-.53.53-.53-.53a.75.75 0 011.06 0z" />
                                    </svg><span>Satellite channels</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M15.362 5.214A8.252 8.252 0 0112 21 8.25 8.25 0 016.038 7.048 8.287 8.287 0 009 9.6a8.983 8.983 0 013.361-6.867 8.21 8.21 0 003 2.48z" />
                                    </svg><span>Heating</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M6 20.25h12m-7.5-3v3m3-3v3m-10.125-3h17.25c.621 0 1.125-.504 1.125-1.125V4.875c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125z" />
                                    </svg><span>Flat-screen TV</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M3.75 3v11.25A2.25 2.25 0 006 16.5h12A2.25 2.25 0 0020.25 14.25V3M3.75 3H20.25M3.75 3h.008v.008H3.75V3zm16.5 0h.008v.008H20.25V3zM3.75 6h.008v.008H3.75V6zm16.5 0h.008v.008H20.25V6zM3.75 9h.008v.008H3.75V9zm16.5 0h.008v.008H20.25V9z" />
                                    </svg><span>Extra long beds (> 6.5 ft)</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M5.25 21V3.75a2.25 2.25 0 012.25-2.25h9a2.25 2.25 0 012.25 2.25V21M3 21h18M12 3.75h.008v.008H12V3.75z" />
                                    </svg><span>Wardrobe or closet</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M3 3h18M3 7.5h18M3 12h18M3 16.5h18M3 21h18M9 3v18m6-18v18" />
                                    </svg><span>Dining area</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M4.5 10.5L12 3m0 0l7.5 7.5M12 3v18" />
                                    </svg><span>Upper floors accessible by stairs only</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M15.75 10.5V6a3.75 3.75 0 10-7.5 0v4.5m11.356-1.993l1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 01-1.12-1.243l1.264-12A1.125 1.125 0 015.513 7.5h12.974c.576 0 1.059.435 1.119 1.007zM8.625 10.5a.375.375 0 11-.75 0 .375.375 0 01.75 0zm7.5 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                                    </svg><span>Clothes rack</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M3.75 3v11.25A2.25 2.25 0 006 16.5h12A2.25 2.25 0 0020.25 14.25V3M3.75 3H20.25M3.75 3h.008v.008H3.75V3zm16.5 0h.008v.008H20.25V3zM3.75 6h.008v.008H3.75V6zm16.5 0h.008v.008H20.25V6zM3.75 9h.008v.008H3.75V9zm16.5 0h.008v.008H20.25V9z" />
                                    </svg><span>Fold-up bed</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M18.375 12.739l-7.693 7.693a4.5 4.5 0 01-6.364-6.364l10.94-10.94A3 3 0 1119.5 7.372L8.552 18.32m.009-.01l-.01.01m7.692-7.693a.75.75 0 00-1.06-1.062L10.5 13.182l-1.96-1.96a.75.75 0 00-1.06 1.061l1.96 1.96-1.484 1.484a.75.75 0 001.06 1.061l1.484-1.484 1.96 1.96a.75.75 0 001.06-1.06l-1.96-1.96 7.693-7.693z" />
                                    </svg><span>Board games/puzzles</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
                                    </svg><span>Books, DVDs or music for children</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z" />
                                    </svg><span>Single-room AC for guest accommodation</span></div>
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 01.865-.501 48.172 48.172 0 003.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3C9.25 3 6.7 3.284 4.293 3.513 2.71 3.746 1.587 5.14 1.587 6.741v6.018z" />
                                    </svg><span>Hand sanitizer</span></div>
                            </div>
    
                            <hr class="section-divider">
    
                            <h2>Smoking:</h2>
                            <div class="amenities-grid">
                                <div class="amenity-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
                                    </svg><span>No smoking</span></div>
                            </div>

                    </div>
                    <aside class="booking-panel">
                        <div class="booking-panel-price">$139<span class="per-night">/ night</span></div>
                        <p class="price-info">Only a few suites left - book now before it’s gone!</p>
                        <a href="#" class="btn-primary" id="book-suite-btn">Book This Suite</a>
                    </aside>
                </div>
            </div>
        </section>
    </main>

    <section class="testimonial-slider-section">
        <div class="container">
            <h2 class="section-title">What Our Guests Say</h2>
        </div>

        <div class="testimonial-slider-container" id="testimonial-slider">
            <!-- Testimonial Cards will be dynamically inserted here by JavaScript -->
        </div>

        <div class="slider-nav">
            <button id="prev-btn" class="slider-btn" aria-label="Previous testimonial">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
                </svg>
            </button>
            <button id="next-btn" class="slider-btn" aria-label="Next testimonial">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                </svg>
            </button>
        </div>
    </section>

    <footer id="footer">
        <!-- Paste your updated footer code here -->
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Atfel Kasbah</h3>
                    <p>Experience authentic Moroccan hospitality in the heart of Ourika Valley.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="index.html#rooms">Rooms & Suites</a></li>
                        <li><a href="gallery.html">Gallery</a></li>
                        <li><a href="aboutus.html">About Us</a></li>
                        <li><a href="blog.html">Blog / News</a></li>
                        <li><a href="faq.html">FAQ</a></li>
                        <li><a href="contactus.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <ul>
                        <li> Ourika Valley, Morocco</li>
                        <li> +212652883513</li>
                        <li><EMAIL></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms & Conditions</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>© 2025 Atfel Kasbah Hotel. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Booking Modal HTML -->
    <div id="booking-overlay" class="booking-overlay">
        <div id="booking-modal" class="booking-modal">
            <button id="modal-close-btn" class="modal-close-btn" aria-label="Close booking options">×</button>

            <h3>Book through our partners:</h3>
            <div class="partner-buttons">
                <a href="https://www.booking.com" target="_blank" rel="noopener noreferrer" class="partner-btn"
                    aria-label="Book on Booking.com">
                    <img src="assets/booking-com-seeklogo.png" alt="Booking.com Logo">
                </a>
                <a href="https://www.expedia.com" target="_blank" rel="noopener noreferrer" class="partner-btn"
                    aria-label="Book on Expedia">
                    <img src="assets/960px-Expedia_Logo_2023.svg.png" alt="Expedia Logo">
                </a>
            </div>

            <h3>Book directly <span class="recommended-text">"Recommended"</span>:</h3>
            <div class="direct-booking-buttons">
                <a href="https://web.whatsapp.com/send/?phone=212652883513&text&type=phone_number&app_absent=0" target="_blank" rel="noopener noreferrer"
                    class="direct-btn whatsapp-btn">
                    <svg fill="currentColor" viewBox="0 0 24 24">
                        <path
                            d="M12.04 2c-5.46 0-9.91 4.45-9.91 9.91 0 1.75.46 3.45 1.32 4.95L2 22l5.25-1.38c1.45.79 3.08 1.21 4.79 1.21 5.46 0 9.91-4.45 9.91-9.91S17.5 2 12.04 2zM12.04 20.15c-1.48 0-2.92-.4-4.2-1.15l-.3-.18-3.12.82.83-3.04-.2-.31c-.82-1.31-1.26-2.83-1.26-4.38 0-4.54 3.68-8.22 8.22-8.22 4.54 0 8.22 3.68 8.22 8.22s-3.68 8.22-8.22 8.22zm4.52-6.14c-.25-.12-1.47-.72-1.7-.82s-.39-.12-.56.12c-.17.25-.64.82-.79.98-.15.17-.29.19-.54.06-.25-.12-1.06-.39-2.02-1.24-.75-.66-1.25-1.48-1.4-1.73-.15-.25-.02-.38.11-.51.11-.11.25-.29.37-.43.12-.15.17-.25.25-.41.08-.17.04-.31-.02-.43s-.56-1.34-.76-1.84c-.2-.48-.41-.42-.56-.42h-.48c-.17 0-.43.06-.66.31-.22.25-.86.85-.86 2.07s.88 2.4 1 2.56c.12.17 1.73 2.63 4.2 3.7.59.26 1.05.41 1.41.52.59.17 1.13.15 1.55.09.48-.06 1.47-.6 1.68-1.18.21-.58.21-1.07.15-1.18-.06-.12-.23-.19-.48-.31z" />
                    </svg>
                    <span>Chat on WhatsApp</span>
                </a>
                <a href="tel:+212652883513" class="direct-btn call-btn">
                    <svg fill="currentColor" viewBox="0 0 24 24">
                        <path
                            d="M20.01 15.38c-1.23 0-2.42-.2-3.53-.56-.35-.12-.74-.03-1.01.24l-1.57 1.97c-2.83-1.35-5.48-3.9-6.89-6.83l1.95-1.66c.27-.28.35-.67.24-1.02-.37-1.11-.56-2.3-.56-3.53 0-.54-.45-.99-.99-.99H4.19C3.65 3 3 3.24 3 3.99 3 13.28 10.73 21 20.01 21c.71 0 .99-.63.99-1.18v-4.43c0-.54-.45-.99-.99-.99z" />
                    </svg>
                    <span>Call Us Directly</span>
                </a>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/swiper/swiper-bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- Mobile Navigation ---
            const navToggle = document.querySelector('.mobile-nav-toggle');
            const primaryNav = document.getElementById('primary-navigation');
            if (navToggle) {
                navToggle.addEventListener('click', () => {
                    const isVisible = primaryNav.getAttribute('data-visible') === 'true';
                    primaryNav.setAttribute('data-visible', !isVisible);
                    navToggle.setAttribute('aria-expanded', !isVisible);
                });
            }

            // --- DYNAMIC GALLERY SCRIPT ---
            const imageFolder = 'Suites/IGRANE-Suite/';
            const imageFiles = [
                'Best-luxury-travel-hotel-Kasbah-atfel-ourika-in-morocco-summer-Mounatinview-hotel3.jpg',
                'Best-luxury-travel-hotel-Kasbah-atfel-ourika-in-morocco-summer-Mounatinview-hotel1.jpg',
                'Best-luxury-travel-hotel-Kasbah-atfel-ourika-in-morocco-summer-Mounatinview-hotel.jpg',
                'Best-luxury-travel-hotel-Kasbah-atfel-ourika-in-morocco-summer-Mounatinview-hotel2.jpg',
                'Best-luxury-travel-hotel-Kasbah-atfel-ourika-in-morocco-summer-Mounatinview-hotel3.jpg',
                
            ];
            const mainWrapper = document.querySelector('.gallery-main .swiper-wrapper');
            const thumbsWrapper = document.querySelector('.gallery-thumbs .swiper-wrapper');
            if (mainWrapper && thumbsWrapper) {
                imageFiles.forEach(file => {
                    const imagePath = imageFolder + file;
                    const altText = `View of the IGRANE Deluxe Suite at Atfel Kasbah hotel in Ourika, Morocco.`;

                    const mainSlide = document.createElement('div');
                    mainSlide.className = 'swiper-slide';
                    mainSlide.innerHTML = `<img src="${imagePath}" alt="${altText}">`;
                    mainWrapper.appendChild(mainSlide);

                    const thumbSlide = document.createElement('div');
                    thumbSlide.className = 'swiper-slide';
                    thumbSlide.innerHTML = `<img src="${imagePath}" alt="Thumbnail of ${altText}">`;
                    thumbsWrapper.appendChild(thumbSlide);
                });
            }
            var galleryThumbs = new Swiper('.gallery-thumbs', {
                spaceBetween: 10,
                slidesPerView: 4,
                freeMode: true,
                watchSlidesProgress: true,
                breakpoints: { 768: { slidesPerView: 6 }, 992: { slidesPerView: 7 } }
            });
            var galleryMain = new Swiper('.gallery-main', {
                spaceBetween: 10,
                navigation: { nextEl: '.swiper-button-next', prevEl: '.swiper-button-prev', },
                thumbs: { swiper: galleryThumbs, },
            });

            // --- BOOKING MODAL SCRIPT ---
            const bookSuiteBtn = document.getElementById('book-suite-btn');
            const bookingOverlay = document.getElementById('booking-overlay');
            const modalCloseBtn = document.getElementById('modal-close-btn');

            if (bookSuiteBtn && bookingOverlay && modalCloseBtn) {
                const openModal = () => {
                    bookingOverlay.classList.add('active');
                    document.body.classList.add('modal-open');
                };

                const closeModal = () => {
                    bookingOverlay.classList.remove('active');
                    document.body.classList.remove('modal-open');
                };

                bookSuiteBtn.addEventListener('click', (e) => {
                    e.preventDefault(); // Prevent link from navigating
                    openModal();
                });

                modalCloseBtn.addEventListener('click', closeModal);

                // Close modal if user clicks on the overlay background
                bookingOverlay.addEventListener('click', (e) => {
                    if (e.target === bookingOverlay) {
                        closeModal();
                    }
                });
            }
        });
    </script>
    <!-- Lightbox HTML Structure -->
    <div id="lightbox-overlay" class="lightbox-overlay">
        <div class="lightbox-container">
            <img id="lightbox-image" class="lightbox-image" src="" alt="">
            <div class="lightbox-close" id="lightbox-close">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </div>
            <div class="lightbox-nav">
                <div class="lightbox-nav-btn" id="lightbox-prev">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
                    </svg>
                </div>
                <div class="lightbox-nav-btn" id="lightbox-next">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/main.js" defer></script>
    <script src="hamburger-menu-fix.js" defer></script>

    <script>
        // Lightbox functionality
        document.addEventListener('DOMContentLoaded', () => {
            const lightboxOverlay = document.getElementById('lightbox-overlay');
            const lightboxImage = document.getElementById('lightbox-image');
            const lightboxClose = document.getElementById('lightbox-close');
            const lightboxPrev = document.getElementById('lightbox-prev');
            const lightboxNext = document.getElementById('lightbox-next');

            let currentImageIndex = 0;
            const images = [];

            // Collect all gallery images
            const galleryImages = document.querySelectorAll('.gallery-main .swiper-slide img');
            galleryImages.forEach((img, index) => {
                images.push({
                    src: img.src,
                    alt: img.alt
                });

                // Make each image clickable to open lightbox
                img.addEventListener('click', () => {
                    openLightbox(index);
                });
            });

            function openLightbox(index) {
                currentImageIndex = index;
                lightboxImage.src = images[index].src;
                lightboxImage.alt = images[index].alt;
                lightboxOverlay.classList.add('active');
                document.body.classList.add('modal-open');
            }

            function closeLightbox() {
                lightboxOverlay.classList.remove('active');
                document.body.classList.remove('modal-open');
            }

            function showPrevImage() {
                currentImageIndex = (currentImageIndex - 1 + images.length) % images.length;
                lightboxImage.src = images[currentImageIndex].src;
                lightboxImage.alt = images[currentImageIndex].alt;
            }

            function showNextImage() {
                currentImageIndex = (currentImageIndex + 1) % images.length;
                lightboxImage.src = images[currentImageIndex].src;
                lightboxImage.alt = images[currentImageIndex].alt;
            }

            // Event listeners
            lightboxClose.addEventListener('click', closeLightbox);
            lightboxPrev.addEventListener('click', showPrevImage);
            lightboxNext.addEventListener('click', showNextImage);

            // Close lightbox when clicking outside the image
            lightboxOverlay.addEventListener('click', (e) => {
                if (e.target === lightboxOverlay) {
                    closeLightbox();
                }
            });

            // Keyboard navigation
            document.addEventListener('keydown', (e) => {
                if (!lightboxOverlay.classList.contains('active')) return;

                if (e.key === 'Escape') {
                    closeLightbox();
                } else if (e.key === 'ArrowLeft') {
                    showPrevImage();
                } else if (e.key === 'ArrowRight') {
                    showNextImage();
                }
            });

            // Touch swipe functionality for mobile
            let touchStartX = 0;
            let touchEndX = 0;

            lightboxImage.addEventListener('touchstart', (e) => {
                touchStartX = e.changedTouches[0].screenX;
            });

            lightboxImage.addEventListener('touchend', (e) => {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            });

            function handleSwipe() {
                const swipeThreshold = 50;
                if (touchEndX < touchStartX - swipeThreshold) {
                    // Swipe left, show next image
                    showNextImage();
                } else if (touchEndX > touchStartX + swipeThreshold) {
                    // Swipe right, show previous image
                    showPrevImage();
                }
            }
        });
    </script>
</body>

</html>