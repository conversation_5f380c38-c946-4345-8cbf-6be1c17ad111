<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discover Atfel Kasbah: An Oasis of Tranquility | Atfel Kasbah Ourika Morocco Blog</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="hamburger-menu-fix.css">
    <link rel="stylesheet" href="assets/css/language-switcher.css">
    
    <!-- Fallback for when JavaScript is disabled -->
    <noscript>
        <style>
            .mobile-nav-toggle { display: none; }
            .nav-links { 
                display: block !important; 
                position: static !important;
                transform: none !important;
                background: transparent !important;
                width: 100% !important;
                pointer-events: auto !important;
            }
        </style>
    </noscript>
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #f7f5f2;
            --text-dark: #2a2a2a;
            --text-light: #6b6b6b;
            --white: #ffffff;
            --border-color: #e5e0da;
        }

        /* --- HEADER & PAGE STRUCTURE --- */
        .page-hero {
            padding-top: 120px;
            padding-bottom: 40px;
            background-color: var(--white);
            text-align: center;
        }
        .page-hero .post-category {
            color: var(--primary-color);
            font-weight: 600;
            text-transform: uppercase;
        }
        .page-hero h1 {
            font-size: 2.8rem;
            color: var(--text-dark);
            max-width: 800px;
            margin: 0.5rem auto 1rem;
        }
        .page-hero .post-meta {
            color: var(--text-light);
        }

        /* --- ARTICLE SECTION --- */
        .post-article-section {
            padding: 40px 0 60px;
            background-color: var(--secondary-color);
        }
        .post-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: var(--white);
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.07);
        }
        .post-hero-image {
            width: 100%;
            height: 400px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 3rem;
        }
        .post-content {
            font-size: 1.15rem;
            line-height: 1.8;
            color: var(--text-light);
        }
        .post-content h2 {
            font-size: 1.8rem;
            color: var(--text-dark);
            margin: 2.5rem 0 1.5rem;
        }
        .post-content p { margin-bottom: 1.5rem; }
        
        /* --- BLOCKQUOTE & FOOTER --- */
        .blockquote {
            border-left: 5px solid var(--primary-color);
            padding-left: 1.5rem;
            font-style: italic;
            font-size: 1.4rem;
            color: var(--text-dark);
            margin: 3rem 0;
            font-weight: 500;
        }
        .post-footer {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
            text-align: center;
        }
        .back-to-blog {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
        }

        /* --- CORRECTED IMAGE STYLES START HERE --- */
        
        .image-with-caption {
            margin: 1rem 0 2rem 0;
            text-align: center; /* Center the container's content */
        }
        .image-with-caption img, .image-right, .image-left {
            max-width: 100%; 
            height: auto;    
            border-radius: 8px;
            display: inline-block; /* Allows centering via text-align on parent */
        }
        .image-caption {
            font-size: 0.9rem;
            color: var(--text-light);
            text-align: center;
            margin-top: 0.5rem;
        }

        /* Floating images */
        .image-right {
            float: right;
            width: 45%;
            margin: 0.5rem 0 1rem 2rem; 
        }
        .image-left {
            float: left;
            width: 45%;
            margin: 0.5rem 2rem 1rem 0; 
        }

        /* Crucial: Clearfix for floated elements */
        .post-content::after {
            content: "";
            display: table;
            clear: both;
        }
        
        /* --- RESPONSIVE ADJUSTMENTS --- */
        @media (max-width: 768px) {
            .page-hero {
                padding-top: 100px;
                padding-bottom: 30px;
            }
            .page-hero h1 {
                font-size: 2rem;
            }
            .post-container {
                padding: 1.5rem;
            }

            /* Un-float images on mobile */
            .image-right, .image-left {
                float: none;
                width: 100%;
                margin: 1rem 0;
            }
        }
    </style>
</head>
<body>
    <header id="header">
        <nav class="container">
            <a href="index.html" class="logo">Atfel Kasbah</a>
            <button class="mobile-nav-toggle" aria-controls="primary-navigation" aria-expanded="false"><span class="line line-top"></span><span class="line line-middle"></span><span class="line line-bottom"></span></button>
            <ul id="primary-navigation" data-visible="false" class="nav-links">
                <li><a href="index.html#home">Home</a></li>
                <li><a href="index.html#rooms">Rooms</a></li>
                <li><a href="gallery.html">Gallery</a></li>
                <li class="cta-item"><a href="index.html#rooms" class="cta-button">Book now</a></li>
                <li><a href="aboutus.html">About Us</a></li>
                <li><a href="contactus.html">Contact</a></li>

                <!-- Language Switcher -->
                <li class="language-switcher">
                    <div class="language-dropdown">
                        <button class="language-btn" aria-label="Switch language">
                            <span class="flag-icon flag-en"></span>
                            <span class="language-text">EN</span>
                        </button>
                        <div class="language-menu">
                            <a href="blog-post-1.html" class="language-option active" data-lang="en">
                                <span class="flag-icon flag-en"></span>
                                <span>English</span>
                            </a>
                            <a href="blog-post-1-fr.html" class="language-option" data-lang="fr">
                                <span class="flag-icon flag-fr"></span>
                                <span>Français</span>
                            </a>
                        </div>
                    </div>
                </li>
            </ul>
        </nav>
    </header>

    <main>
        <section class="page-hero">
            <div class="post-category">Hotel Life</div>
            <h1>Discover Atfel Kasbah: An Oasis of Tranquility</h1>
            <div class="post-meta">Published on July 18, 2025</div>
        </section>

        <section class="post-article-section">
            <div class="container post-container">
                <img src="assets/image.png" alt="Peaceful courtyard of Atfel Kasbah" class="post-hero-image">

                <div class="post-content">
                    <p>When you first arrive at Atfel Kasbah, the first thing you notice isn’t the grandeur, but the quiet. It’s a profound sense of peace that seems to rise from the very earth of the Ourika Valley, a gentle welcome that promises rest and rejuvenation. Our kasbah was designed not merely as a place to stay, but as a sanctuary to experience.  Located in the heart of the valley, it serves as a true escape from the bustle of Marrakech. </p>

                    <h2>Architecture that Breathes</h2>
                    <p>The walls, built with traditional pisé (rammed earth), stay cool in the summer and warm in the winter, breathing with the seasons.  Every archway frames a new view—the snow-capped Atlas peaks, the lush green of our gardens, or the inviting turquoise of the pool.  We worked with local artisans to ensure every detail, from the hand-carved wooden doors to the intricate zellij tilework, honors the rich heritage of Moroccan craftsmanship.</p>
                    
                    <div class="image-with-caption">
                        <img src="Gallery-Hotel_Kasbah_Atfel_Ourika/affordable-hotel-Kasbah-atfel-ourika-in-morocco10.jpg" alt="Intricate zellij tilework">
                        <p class="image-caption">A detail of the traditional zellij tilework found throughout the Kasbah.</p>
                    </div>

                    <p>The influence of Berber design is everywhere. The use of natural materials, the earthy tones, and the focus on symmetry and balance contribute to the feeling of harmony that permeates the entire property. It’s a space where you can shed the noise of everyday life and reconnect with a slower, more mindful pace.</p>

                    <h2>Spaces for Serenity</h2>
                    <p>Find your favorite nook. Perhaps it's a plush armchair by the grand fireplace in the salon, a shaded daybed by the year-round outdoor pool, or a private corner on our rooftop terrace with a 360-degree view of the valley.  These spaces were created for quiet reflection, deep conversation, or simply losing yourself in a good book with a pot of fresh mint tea by your side.</p>
                    
                    <img src="Gallery-Hotel_Kasbah_Atfel_Ourika/affordable-hotel-Kasbah-atfel-ourika-in-morocco5.jpg" alt="Ourika Valley views from the Kasbah" class="image-right">
                    
                    <p>The salon offers a perfect setting for evenings. You can relax and chat while enjoying the soft, warm glow of a fire as the sun sets over the Ourika Valley. For those seeking more social interaction, the open-air dining area provides a stunning backdrop for sharing a meal, with a view that's as memorable as the food.</p>
                    <p>And when the day is done, suites with private balconies looking out over the Atlas Mountains await.  These rooms are the ultimate escape into luxury and allow you to fully experience the peace that Atfel Kasbah offers, far from the rush of daily life.</p>
                    
                    <div class="image-with-caption">
                        <img src="Gallery-Hotel_Kasbah_Atfel_Ourika/affordable-hotel-Kasbah-atfel-ourika-in-morocco19.jpg" alt="Rooftop Terrace with Mountain view">
                        <p class="image-caption">The rooftop terrace at sunset, ideal for enjoying the stunning Ourika Valley views.</p>
                    </div>
                    
                    <blockquote class="blockquote">"Atfel Kasbah isn't just a destination; it's a feeling. A return to a slower, more intentional way of living."</blockquote>
                    
                    <p>Each morning, you're invited to experience breakfast, which is available for a surcharge.  Made with the freshest local ingredients, it provides a flavorful introduction to the culture and will set you up perfectly for the best day possible in Ourika.</p>

                    <h2>More than a stay, It's an experience</h2>
                    <p>Atfel Kasbah is a retreat. It's an invitation to slow down, savor each moment, and create memories that will last a lifetime. Whether you're an adventurer seeking thrilling hikes and walking tours or someone in need of total relaxation, Atfel Kasbah awaits, ready to deliver the perfect escape. </p>

                    <img src="Spa/Hotel-Kasbah-Atfel-Spa-Jacuzzi.jpg" alt="Breakfast at Atfel Kasbah" class="image-left">
                    
                    <p>Our on-site spa, a place of complete serenity, will let you experience complete relaxation.  Enjoy our selection of spa treatments, including massages and a traditional hammam, and get ready to leave us totally refreshed. </p>
                </div>

                <footer class="post-footer">
                    <a href="blog.html" class="back-to-blog">← Back to All Articles</a>
                </footer>
            </div>
        </section>
    </main>

    <footer id="footer">
        <!-- Paste your updated footer code here -->
        <div class="container">
            <div class="footer-content">
                <div class="footer-section"><h3>Atfel Kasbah</h3><p>Experience authentic Moroccan hospitality in the heart of Ourika Valley.</p></div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="index.html#rooms">Rooms & Suites</a></li>
                        <li><a href="gallery.html">Gallery</a></li>
                        <li><a href="aboutus.html">About Us</a></li>
                        <li><a href="blog.html">Blog / News</a></li>
                        <li><a href="faq.html">FAQ</a></li>
                        <li><a href="contactus.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <ul><li>📍 Douar Boutbira, CR Ourika, 42452 Ourika, Morocco</li><li>📞 +212652883513</li><li>✉️ <EMAIL></li></ul>
                </div>
                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul><li><a href="#">Privacy Policy</a></li><li><a href="#">Terms & Conditions</a></li></ul>
                </div>
            </div>
            <div class="footer-bottom"><p>© 2025 Atfel Kasbah Hotel. All rights reserved.</p></div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const navToggle = document.querySelector('.mobile-nav-toggle');
            const primaryNav = document.getElementById('primary-navigation');
            if (navToggle) {
                navToggle.addEventListener('click', () => {
                    const isVisible = primaryNav.getAttribute('data-visible') === 'true';
                    primaryNav.setAttribute('data-visible', !isVisible);
                    navToggle.setAttribute('aria-expanded', !isVisible);
                });
            }
        });
    </script>
    <script src="assets/js/language-switcher.js" defer></script>
</body>
</html>