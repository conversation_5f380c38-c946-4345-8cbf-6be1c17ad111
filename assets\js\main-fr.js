document.addEventListener('DOMContentLoaded', () => {

    // --- Mobile Navigation --- //
    const nav = document.querySelector('.nav-links');
    const navToggle = document.querySelector('.mobile-nav-toggle');

    if (navToggle && nav) {
        navToggle.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            const isVisible = nav.getAttribute('data-visible');
            if (isVisible === 'false') {
                nav.setAttribute('data-visible', 'true');
                navToggle.setAttribute('aria-expanded', 'true');
            } else {
                nav.setAttribute('data-visible', 'false');
                navToggle.setAttribute('aria-expanded', 'false');
            }
        });

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (nav.getAttribute('data-visible') === 'true' &&
                !nav.contains(e.target) &&
                e.target !== navToggle &&
                !navToggle.contains(e.target)) {
                nav.setAttribute('data-visible', 'false');
                navToggle.setAttribute('aria-expanded', 'false');
            }
        });

        // Close menu when clicking on navigation links
        const navLinks = nav.querySelectorAll('a');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                nav.setAttribute('data-visible', 'false');
                navToggle.setAttribute('aria-expanded', 'false');
            });
        });
    }

    // --- Header Scroll Effects --- //
    const header = document.querySelector('header');
    const heroSection = document.querySelector('.hero');

    const heroObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (!entry.isIntersecting) {
                header.classList.add('header-cta-visible');
            } else {
                header.classList.remove('header-cta-visible');
            }
        });
    }, { threshold: 0.1 });

    if (heroSection) {
        heroObserver.observe(heroSection);
    }

    window.addEventListener('scroll', () => {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });

    // --- Typing Effect with French phrases --- //
    const typingElement = document.getElementById('typing-subtitle');
    if (typingElement) {
        // This checks for an inline script setup which you might not be using, but it's safe to keep.
        const phrasesAttr = typingElement.getAttribute('data-phrases');
        const phrases = phrasesAttr ? JSON.parse(phrasesAttr) : [
            "Votre maison loin de chez vous.",
            "L'une des plus belles vues du Maroc.",
            "Découvrez le charme authentique dans la vallée d'Ourika."
        ];

        let phraseIndex = 0;
        let charIndex = 0;
        let isDeleting = false;

        function type() {
            const currentPhrase = phrases[phraseIndex];
            if (isDeleting) {
                charIndex--;
            } else {
                charIndex++;
            }

            typingElement.textContent = currentPhrase.substring(0, charIndex);

            let typeSpeed = isDeleting ? 75 : 150;

            if (!isDeleting && charIndex === currentPhrase.length) {
                typeSpeed = 2000; // Pause at end
                isDeleting = true;
            } else if (isDeleting && charIndex === 0) {
                isDeleting = false;
                phraseIndex = (phraseIndex + 1) % phrases.length;
                typeSpeed = 500; // Pause before new phrase
            }

            setTimeout(type, typeSpeed);
        }
        if (phrases.length > 0) type();
    }

    // --- Logo Scroller --- //
    const scroller = document.querySelector('.logo-scroller-inner');
    if (scroller) {
        const scrollerContent = Array.from(scroller.children);
        scrollerContent.forEach(item => {
            const duplicatedItem = item.cloneNode(true);
            duplicatedItem.setAttribute('aria-hidden', true);
            scroller.appendChild(duplicatedItem);
        });
    }

    // --- Room Filtering and 'View More' --- //
    const filterContainer = document.querySelector('.filter-controls');
    if (filterContainer) {
        const allRoomCards = Array.from(document.querySelectorAll('.room-card'));
        const viewMoreBtn = document.getElementById('view-more-btn');
        const roomsToShowInitially = 4;

        // This filters based on buttons and shows all that match.
        const filterRooms = (filter) => {
            let visibleCards = [];
            allRoomCards.forEach(card => {
                if (filter === 'all' || card.dataset.category === filter) {
                    card.style.display = 'block';
                    visibleCards.push(card);
                } else {
                    card.style.display = 'none';
                }
            });
            // Hide 'view more' during filtering. Re-evaluate if needed.
            if (viewMoreBtn) viewMoreBtn.style.display = 'none';
        }

        // This function is for the initial load of the 'all' tab
        const setupInitialRoomView = () => {
            if (!viewMoreBtn) return; // Only run if view more button exists

            allRoomCards.forEach((card, index) => {
                if (index < roomsToShowInitially) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });

            if (allRoomCards.length > roomsToShowInitially) {
                viewMoreBtn.style.display = 'inline-block';
            } else {
                viewMoreBtn.style.display = 'none';
            }
        };

        filterContainer.addEventListener('click', (e) => {
            if (e.target.matches('.filter-btn')) {
                filterContainer.querySelector('.active')?.classList.remove('active');
                e.target.classList.add('active');
                const filterValue = e.target.getAttribute('data-filter');
                filterRooms(filterValue);
            }
        });

        if (viewMoreBtn) {
            viewMoreBtn.addEventListener('click', () => {
                allRoomCards.forEach(card => {
                    card.style.display = 'block';
                });
                viewMoreBtn.style.display = 'none';
            });
        }

        // Setup initial view
        // We assume the 'All' button is active by default.
        setupInitialRoomView();
    }


    // --- Custom Testimonial Slider --- //
    const reviewsData = [
     {
        author: "Eline, Belgium",
        review: "Nous avons été très satisfaits de notre séjour. La kasbah offre des chambres spacieuses et décorées avec goût, ainsi qu’un espace extérieur serein avec une piscine bien entretenue. L’atmosphère est chaleureuse et accueillante, empreinte de calme. L’hôtesse nous a reçus avec beaucoup de gentillesse et, à notre arrivée, nous avons été accueillis par un subtil parfum de fleurs — un accueil discret et mémorable."
     },
     {
        author: "Liana, UK",
        review: "Tout à la Kasbah est parfait. La chambre était exceptionnelle, Aziz et Lessen ont été très serviables et toujours disponibles pour aider. La piscine est magnifique et rafraîchissante. L’ensemble du domaine est un véritable plaisir. Si vous cherchez un endroit relaxant, n’hésitez pas à réserver la Kasbah Atfel 🥰"
     },
     {
        author: "Sinead, Ireland",
        review: "Nous avons adoré chaque aspect de cet établissement. C’est un bâtiment magnifique avec une superbe piscine. Les chambres sont très confortables et propres. Bien que la nourriture fût délicieuse, le meilleur moment de notre séjour a été grâce au personnel, qui était exceptionnel."
     },
     {
        author: "Ahmed, Netherlands",
        review: "Nous avons passé un excellent séjour dans cet hôtel/riad. Les chambres étaient très spacieuses et joliment décorées. La piscine est également très agréable. On ressent constamment l’ambiance des Mille et Une Nuits durant le séjour. Aziz est un homme adorable et un hôte exceptionnel, qui nous a offert tout le confort possible. Nous le recommandons absolument et reviendrons sans aucun doute."
     },
     {
        author: "Hils, Australia",
        review: "Tellement relaxant après le tumulte de Marrakech. J’ai rencontré des personnes adorables, même si l’endroit était plutôt calme. On se sent un peu comme une star de cinéma avec toute l’attention portée, et la qualité de la nourriture (beaucoup produite sur place) était fantastique. De magnifiques jardins."
     },
     {
        author: "Adil, Morocco",
        review: "Excellente situation, en pleine nature avec une atmosphère saine. Les hôtes sont très accueillants. La nourriture est délicieusement faite maison et servie avec soin et finesse. C’est l’endroit idéal pour régénérer l’esprit."
     },
     {
        author: "Caroline, Switzerland",
        review: "La maison d’hôtes est splendide et située dans un endroit paisible. La chambre était immense avec une décoration marocaine moderne. Il y a un joli jardin et une grande piscine. Aziz et sa femme sont des personnes adorables, toujours soucieux de notre confort. La nourriture est délicieuse et les prix sont raisonnables. Merci pour votre accueil, nous avons vraiment apprécié notre séjour, c’était très relaxant. Je recommande à 100 %."
     },
     {
        author: "Cecile, South Africa",
        review: "La kasbah est magnifiquement conçue et très paisible, avec une vue à 360°. Le personnel était extrêmement serviable et sympathique, la nourriture savoureuse et les lits confortables. Un excellent rapport qualité-prix !"
     },
     {
        author: "Tom, UK",
        review: "Un endroit charmant pour partir à la découverte des montagnes de l’Atlas. Les jardins et la piscine sont magnifiques et paisibles. Le personnel était attentionné — notamment Abdo qui nous a emmenés pour une fantastique journée dans l’Atlas, avec randonnée, déjeuner et visites variées. Nous lui sommes très reconnaissants pour son dévouement. Le domaine était parfait pour un séjour simple et agréable. La piscine est très grande (même si non chauffée) et nous avons passé des soirées très relaxantes dans la tranquillité de la kasbah."
     },
     {
        author: "Claudio, Italy",
        review: "Nous avons littéralement tout adoré ! Le bâtiment est magnifique et idéalement situé, notre chambre était propre, spacieuse et élégante. C’est impressionnant de découvrir la kasbah pour la première fois depuis la rue ! Après une journée d’exploration, nous avons dégusté un délicieux dîner sur place. Les lits sont extrêmement confortables et la chambre très silencieuse. Avant de partir, nous avons également passé un moment merveilleux dans le jardin splendide avec sa belle piscine. Le personnel est incroyablement aimable et attentionné, il s’occupe de tout ! Nous recommandons vivement de venir les visiter !"
     }
    ];


    const sliderContainer = document.getElementById('testimonial-slider');

    if (sliderContainer) {
        let currentIndex = 0;
        let isDragging = false;
        let startPos = 0;
        let currentTranslate = 0;

        // Create and append review cards
        reviewsData.forEach((review, index) => {
            const card = document.createElement('div');
            card.classList.add('testimonial-card');
            card.innerHTML = `
                <div class="testimonial-header">
                    <img src="assets/booking-com-seeklogo.png" alt="Booking.com Logo" class="booking-logo">
                    <span class="testimonial-author">${review.author}</span>
                </div>
                <p class="testimonial-body">${review.review}</p>
            `;
            card.dataset.index = index;
            sliderContainer.appendChild(card);
        });

        const cards = document.querySelectorAll('.testimonial-card');

        function setCardPositions() {
            cards.forEach((card, index) => {
                card.classList.remove('active', 'prev', 'next', 'hidden-left', 'hidden-right');

                if (index === currentIndex) {
                    card.classList.add('active');
                } else if (index === (currentIndex - 1 + cards.length) % cards.length) {
                    card.classList.add('prev');
                } else if (index === (currentIndex + 1) % cards.length) {
                    card.classList.add('next');
                } else {
                    // This logic ensures cards far away are hidden properly
                    const d = index - currentIndex;
                    if (Math.abs(d) > 1) {
                        if (d > 0 && (d < cards.length / 2) || d < -cards.length / 2) {
                            card.classList.add('hidden-right');
                        } else {
                            card.classList.add('hidden-left');
                        }
                    }
                }
            });
        }

        function showNext() {
            currentIndex = (currentIndex + 1) % cards.length;
            setCardPositions();
        }

        function showPrev() {
            currentIndex = (currentIndex - 1 + cards.length) % cards.length;
            setCardPositions();
        }

        document.getElementById('next-btn').addEventListener('click', showNext);
        document.getElementById('prev-btn').addEventListener('click', showPrev);

        // Swipe functionality for mouse and touch
        cards.forEach(card => {
            card.addEventListener('mousedown', dragStart);
            card.addEventListener('touchstart', dragStart, { passive: true });

            card.addEventListener('mouseup', dragEnd);
            card.addEventListener('touchend', dragEnd);
            card.addEventListener('mouseleave', dragEnd);

            card.addEventListener('mousemove', drag);
            card.addEventListener('touchmove', drag, { passive: true });
        });

        function dragStart(event) {
            isDragging = true;
            startPos = getPositionX(event);

            // Apply a class to the card for cursor style
            const activeCard = cards[currentIndex];
            if (activeCard) activeCard.classList.add('grabbing');
        }

        function drag(event) {
            if (isDragging) {
                const currentPosition = getPositionX(event);
                currentTranslate = currentPosition - startPos;
            }
        }

        function dragEnd() {
            if (!isDragging) return;
            isDragging = false;

            if (currentTranslate < -100) { // Swipe left
                showNext();
            } else if (currentTranslate > 100) { // Swipe right
                showPrev();
            }

            currentTranslate = 0; // Reset
            const activeCard = cards[currentIndex];
            if (activeCard) activeCard.classList.remove('grabbing');
        }

        function getPositionX(event) {
            return event.type.includes('mouse') ? event.clientX : event.touches[0].clientX;
        }

        // Disable context menu on drag to prevent interference
        window.oncontextmenu = function (event) {
            if (isDragging) {
                event.preventDefault();
                event.stopPropagation();
                return false;
            }
        };

        // Initialize slider
        setCardPositions();
    }
    // --- Scroll Animation for Luxury Awards --- //
    const awardItems = document.querySelectorAll('.luxury-award-item');

    if (awardItems.length > 0) {
        const awardObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('is-visible');
                    observer.unobserve(entry.target); // Stop observing once it's visible
                }
            });
        }, {
            threshold: 0.1 // Trigger when 10% of the item is visible
        });

        awardItems.forEach(item => {
            awardObserver.observe(item);
        });
    }

});