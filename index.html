<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Atfel Kasbah Hotel - Luxury Accommodation in Ourika Valley, Morocco</title>
    <meta name="description"
        content="Experience authentic Moroccan hospitality at Atfel Kasbah Hotel. Luxury accommodation in the breathtaking Ourika Valley, near Marrakech. Book your perfect getaway today.">
    <meta name="keywords"
        content="Atfel Kasbah, Ourika Valley hotel, Morocco accommodation, Marrakech hotels, luxury kasbah, Atlas Mountains">
    <meta name="robots" content="index, follow">
    <meta name="author" content="Atfel Kasbah Hotel">

    <!-- Open Graph tags for social media -->
    <meta property="og:title" content="Atfel Kasbah Hotel - Luxury Accommodation in Ourika Valley">
    <meta property="og:description"
        content="Experience authentic Moroccan hospitality at Atfel Kasbah Hotel in the breathtaking Ourika Valley.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.atfelkasbah.com">
    <meta property="og:image" content="https://via.placeholder.com/1200x630/8B4513/FFFFFF?text=Atfel+Kasbah+Hotel">

    <!-- Twitter Card tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Atfel Kasbah Hotel - Luxury Accommodation in Ourika Valley">
    <meta name="twitter:description"
        content="Experience authentic Moroccan hospitality at Atfel Kasbah Hotel in the breathtaking Ourika Valley.">
    <meta name="twitter:image" content="https://via.placeholder.com/1200x630/8B4513/FFFFFF?text=Atfel+Kasbah+Hotel">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://www.atfelkasbah.com">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">

    <!-- JSON-LD Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Hotel",
        "name": "Atfel Kasbah Hotel",
        "description": "Luxury accommodation in the breathtaking Ourika Valley, Morocco",
        "address": {
            "@type": "PostalAddress",
            "addressLocality": "Ourika Valley",
            "addressRegion": "Marrakech-Safi",
            "addressCountry": "Morocco"
        },
        "telephone": "+212-XXX-XXXXXX",
        "url": "https://www.atfelkasbah.com",
        "priceRange": "$$$$",
        "amenityFeature": [
            { "@type": "LocationFeatureSpecification", "name": "Free WiFi" },
            { "@type": "LocationFeatureSpecification", "name": "Restaurant" },
            { "@type": "LocationFeatureSpecification", "name": "Spa" }
        ]
    }
    </script>

    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="hamburger-menu-fix.css">
    <link rel="stylesheet" href="assets/css/language-switcher.css">

    <!-- Alternate language links for SEO -->
    <link rel="alternate" hreflang="en" href="https://www.atfelkasbah.com">
    <link rel="alternate" hreflang="fr" href="https://www.atfelkasbah.com/fr">

    <!-- START: Custom CSS -->
    <style>
        /* CSS for Image Collage Effect */
        .experience-block .image-collage {
            position: relative;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .image-collage .collage-image-back,
        .image-collage .collage-image-front {
            position: absolute;
            border-radius: 8px;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            object-fit: cover;
        }

        .image-collage .collage-image-back {
            width: 85%;
            height: 85%;
            z-index: 1;
            transform: translateX(-5%) rotate(-2deg);
        }

        .image-collage .collage-image-front {
            width: 65%;
            height: 65%;
            bottom: 0;
            right: 0;
            z-index: 2;
            border: 5px solid #fff;
            transform: translateX(5%) rotate(3deg);
        }

        .experience-block:hover .collage-image-back {
            transform: translateX(-10%) rotate(-4deg) scale(1.05);
        }

        .experience-block:hover .collage-image-front {
            transform: translateX(10%) rotate(5deg) scale(1.05);
        }

        @media (max-width: 768px) {
            .experience-block .image-collage {
                min-height: 300px;
            }
        }

        /* START: CSS for Amenities Section */
        .amenities-section {
            padding: 4rem 0;
            background-color: #f8f6f0;
            /* A slightly off-white background */
        }

        .amenities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 2rem;
            margin-top: 2.5rem;
            text-align: center;
        }

        .amenity-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .amenity-item:hover {
            transform: translateY(-5px);
        }

        .amenity-icon {
            width: 50px;
            height: 50px;
            margin-bottom: 1rem;
            color: #8B4513;
            /* Primary brand color */
        }

        .amenity-label {
            font-size: 1rem;
            font-weight: 500;
            color: #333;
        }

        @media (max-width: 480px) {
            .amenities-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.5rem;
            }
        }

        /* END: CSS for Amenities Section */
    </style>
    <!-- END: Custom CSS -->


    <!-- Fallback for when JavaScript is disabled -->
    <noscript>
        <style>
            .mobile-nav-toggle {
                display: none;
            }

            .nav-links {
                display: block !important;
                position: static !important;
                transform: none !important;
                background: transparent !important;
                width: 100% !important;
                pointer-events: auto !important;
            }
        </style>
    </noscript>
</head>

<body>
    <header id="header">
        <nav class="container">
            <a href="#" class="logo">Atfel Kasbah</a>

            <button class="mobile-nav-toggle" aria-controls="primary-navigation" aria-expanded="false">
                <span class="line line-top"></span>
                <span class="line line-middle"></span>
                <span class="line line-bottom"></span>
            </button>

            <ul id="primary-navigation" data-visible="false" class="nav-links">
                <li><a href="#home">Home</a></li>
                <li><a href="#rooms">Rooms</a></li>
                <li><a href="gallery.html">Gallery</a></li>
                <li class="cta-item">
                    <a href="#rooms" id="header-cta-button" class="cta-button">Book now</a>
                </li>
                <li><a href="aboutus.html">About Us</a></li>
                <li><a href="contactus.html">Contact</a></li>
                <li class="language-switcher">
                    <div class="language-dropdown">
                        <button class="language-btn" aria-label="Switch language">
                            <span class="flag-icon flag-en"></span>
                            <span class="language-text">EN</span>
                        </button>
                        <div class="language-menu">
                            <a href="index.html" class="language-option active" data-lang="en">
                                <span class="flag-icon flag-en"></span>
                                <span>English</span>
                            </a>
                            <a href="index-fr.html" class="language-option" data-lang="fr">
                                <span class="flag-icon flag-fr"></span>
                                <span>Français</span>
                            </a>
                        </div>
                    </div>
                </li>
            </ul>
        </nav>
    </header>

    <section class="hero" id="home">
        <video playsinline autoplay muted loop id="hero-video">
            <source src="assets\Video-Kasbah-atfel-ourika.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        <div class="hero-content">
            <div class="welcome-line"><span>Welcome — مرحبا — Bonjour</span></div>
            <h1>At Kasbah Atfel</h1>
            <p class="typing-subtitle-container">
                <span id="typing-subtitle"></span><span class="typing-cursor"></span>
            </p>
            <div class="hero-buttons">
                <a href="#rooms" id="hero-cta-button" class="btn-primary">Book now</a>
            </div>
        </div>
    </section>

    <section class="featured-on">
        <div class="container">
            <h2 class="featured-on-title">Trusted & Recommended</h2>
            <div class="logo-scroller">
                <div class="logo-scroller-inner">
                    <a href="#" class="logo-item" aria-label="Rated 4.9 on Maps">
                        <div class="logo-item-top"><img src="assets/google-icon-logo-svgrepo-com.svg"
                                alt="Maps Logo"><span class="partner-name">Maps</span></div>
                        <div class="logo-item-bottom"><span class="rating-value">4.7</span>
                            <div class="stars" aria-label="4.9 out of 5 stars"><svg viewBox="0 0 24 24">
                                    <path fill="currentColor"
                                        d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2l-2.81 6.63L2 9.24l5.46 4.73L5.82 21z" />
                                </svg><svg viewBox="0 0 24 24">
                                    <path fill="currentColor"
                                        d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2l-2.81 6.63L2 9.24l5.46 4.73L5.82 21z" />
                                </svg><svg viewBox="0 0 24 24">
                                    <path fill="currentColor"
                                        d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2l-2.81 6.63L2 9.24l5.46 4.73L5.82 21z" />
                                </svg><svg viewBox="0 0 24 24">
                                    <path fill="currentColor"
                                        d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2l-2.81 6.63L2 9.24l5.46 4.73L5.82 21z" />
                                </svg><svg viewBox="0 0 24 24">
                                    <path fill="currentColor"
                                        d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2l-2.81 6.63L2 9.24l5.46 4.73L5.82 21z" />
                                </svg></div>
                        </div>
                    </a>
                    <a href="#" class="logo-item" aria-label="Rated 9.2 on Booking">
                        <div class="logo-item-top"><img src="assets/booking-com-seeklogo.png" alt="Booking Logo"></div>
                        <div class="partner-rating">
                            <div class="score-box score-box--blue">9.2</div>
                            <div class="rating-text-block"><span class="review-count">300+ reviews</span></div>
                        </div>
                    </a>
                    <a href="#" class="logo-item" aria-label="Rated 9.4 on Expedia">
                        <div class="logo-item-top"><img src="assets/960px-Expedia_Logo_2023.svg.png" alt="Expedia Logo">
                        </div>
                        <div class="partner-rating">
                            <div class="score-box score-box--green">9.4</div><span
                                class="rating-label">Exceptional</span>
                        </div>
                    </a>
                    <a href="#" class="logo-item" aria-label="Rated 9.2 on Kayak">
                        <div class="logo-item-top"><img src="assets/kayak-seeklogo.png" alt="Kayak Logo"></div>
                        <div class="partner-rating">
                            <div class="score-box score-box--orange">9.2</div>
                            <div class="rating-text-block"><span class="review-count">370+ reviews</span></div>
                        </div>
                    </a>
                    <a href="#" class="logo-item" aria-label="Rated 9.5 on Hotels.com">
                        <div class="logo-item-top"><img src="assets/Hotels.com_Logo_2023.svg" alt="Hotels.com Logo">
                        </div>
                        <div class="partner-rating">
                            <div class="score-box score-box--green">9.5</div><span
                                class="rating-label">Exceptional</span>
                        </div>
                    </a>
                    <a href="#" class="logo-item" aria-label="Rated 4.9 on Maps">
                        <div class="logo-item-top"><img src="assets/google-icon-logo-svgrepo-com.svg"
                                alt="Maps Logo"><span class="partner-name">Maps</span></div>
                        <div class="logo-item-bottom"><span class="rating-value">4.7</span>
                            <div class="stars" aria-label="4.9 out of 5 stars"><svg viewBox="0 0 24 24">
                                    <path fill="currentColor"
                                        d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2l-2.81 6.63L2 9.24l5.46 4.73L5.82 21z" />
                                </svg><svg viewBox="0 0 24 24">
                                    <path fill="currentColor"
                                        d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2l-2.81 6.63L2 9.24l5.46 4.73L5.82 21z" />
                                </svg><svg viewBox="0 0 24 24">
                                    <path fill="currentColor"
                                        d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2l-2.81 6.63L2 9.24l5.46 4.73L5.82 21z" />
                                </svg><svg viewBox="0 0 24 24">
                                    <path fill="currentColor"
                                        d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2l-2.81 6.63L2 9.24l5.46 4.73L5.82 21z" />
                                </svg><svg viewBox="0 0 24 24">
                                    <path fill="currentColor"
                                        d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2l-2.81 6.63L2 9.24l5.46 4.73L5.82 21z" />
                                </svg></div>
                        </div>
                    </a>
                    <a href="#" class="logo-item" aria-label="Rated 9.2 on Booking">
                        <div class="logo-item-top"><img src="assets/booking-com-seeklogo.png" alt="Booking Logo"></div>
                        <div class="partner-rating">
                            <div class="score-box score-box--blue">9.2</div>
                            <div class="rating-text-block"><span class="review-count">300+ reviews</span></div>
                        </div>
                    </a>
                    <a href="#" class="logo-item" aria-label="Rated 9.4 on Expedia">
                        <div class="logo-item-top"><img src="assets/960px-Expedia_Logo_2023.svg.png" alt="Expedia Logo">
                        </div>
                        <div class="partner-rating">
                            <div class="score-box score-box--green">9.4</div><span
                                class="rating-label">Exceptional</span>
                        </div>
                    </a>
                    <a href="#" class="logo-item" aria-label="Rated 9.2 on Kayak">
                        <div class="logo-item-top"><img src="assets/kayak-seeklogo.png" alt="Kayak Logo"></div>
                        <div class="partner-rating">
                            <div class="score-box score-box--orange">9.2</div>
                            <div class="rating-text-block"><span class="review-count">370+ reviews</span></div>
                        </div>
                    </a>
                    <a href="#" class="logo-item" aria-label="Rated 9.5 on Hotels.com">
                        <div class="logo-item-top"><img src="assets/Hotels.com_Logo_2023.svg" alt="Hotels.com Logo">
                        </div>
                        <div class="partner-rating">
                            <div class="score-box score-box--green">9.5</div><span
                                class="rating-label">Exceptional</span>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <section class="experience-section" id="experience">
        <div class="container">
            <h2 class="section-title">Experience The Kasbah</h2>
            <div class="experience-block image-left">
                <div class="experience-image"><img src="assets/image.png" alt="Lush gardens of Atfel Kasbah"></div>
                <div class="experience-text">
                    <h3>Luxury Hotel in Atfel Ourika</h3>
                    <p><strong>Kasbah Atfel</strong> - your one‑stop escape to serenity in the heart of the
                        <strong>Ourika Valley</strong>. Lose yourself in the timeless beauty of our <strong>traditional
                            Moroccan gardens</strong> - an aromatic oasis of native flora, gentle fountains and shaded
                        alcoves just made for sipping mint tea and reflecting in peace.
                    </p>

                    <p>Nestled at the foot of the <strong>Atlas Mountains</strong> and close to the valley’s center, the
                        famous <strong>Setti Fatma waterfalls</strong>, the historic <strong>Aït Souka kasbahs</strong>,
                        and the colorful <strong>Tuesday souk at Tahnaout</strong>, <strong>Kasbah Atfel</strong> offers
                        effortless access to the region’s most‑loved attractions.</p>

                    <a href="#" class="btn-secondary">Explore the Grounds</a>
                </div>
            </div>
            <div class="experience-block image-right">
                <div class="experience-image"><img
                        src="Gallery-Hotel_Kasbah_Atfel_Ourika/affordable-hotel-Kasbah-atfel-ourika-in-morocco22.jpg"
                        alt="Authentic Moroccan Tagine"></div>
                <div class="experience-text">
                    <h3>Swimming & Sunset Views</h3>
                    <p>Step into our <strong>large family‑friendly swimming pool</strong>, the ultimate <strong>poolside
                            experience</strong> for all ages. As the sun dips behind the Atlas foothills, savor the
                        <strong>spectacular sunset views</strong> from your lounger-ideal for creating unforgettable
                        memories with loved ones. If you’re searching for “Ourika Valley hotel with pool,”
                        “family‑friendly pool near Marrakech,” or “best sunset views pool Morocco,” look no further than
                        Kasbah Atfel’s inviting waters and evening skies.
                    </p>
                    <a href="#" class="btn-secondary">View Our Menu</a>
                </div>
            </div>

            <!-- START: "TRANQUIL LIFE" SECTION -->
            <div class="experience-block image-left">
                <div class="experience-image image-collage">
                    <img src="Spa/Hotel-Kasbah-Atfel-Spa-Jacuzzi10.jpg"
                        alt="Main view of tranquil gardens at Atfel Kasbah" class="collage-image-back">
                    <img src="Spa/Hotel-Kasbah-Atfel-Spa-Jacuzzi4.jpg"
                        alt="Detailed view of a quiet corner in the gardens" class="collage-image-front">
                </div>
                <div class="experience-text">
                    <h3> Jacuzzi & Hammam Spa</h3>
                    <p>At <strong>Kasbah Atfel</strong>, immerse yourself in true Moroccan wellness. Step into our
                        restored, stone‑walled <strong>traditional hammam</strong>, where gentle steam and handcrafted
                        black soap purify your skin and soothe your spirit. Then unwind in our secluded <strong>heated
                            Jacuzzi</strong>, framed by flickering lanterns and lush greenery. Complete your escape with
                        a signature massage, choose from invigorating argan‑oil scrubs, relaxing full‑body treatments,
                        or targeted back, neck and foot therapies - all delivered in our peaceful <strong>spa
                            lounge</strong> overlooking the Atlas foothills.</p>
                    <a href="#" class="btn-secondary">Explore the Grounds</a>
                </div>
            </div>
            <!-- END: "TRANQUIL LIFE" SECTION -->

            <div class="experience-block image-right">
                <div class="experience-image"><img
                        src="Gallery-Hotel_Kasbah_Atfel_Ourika\Famous-place in-morocco-Ourika-Valley-Mountain-View.jpg"
                        alt="Authentic Moroccan Tagine"></div>
                <div class="experience-text">
                    <h3>Unforgettable Experiences</h3>
                    <p>Just a short stroll from <strong>Kasbah Atfel</strong>, the gentle flow of the <strong>Ourika
                            Valley</strong> invites you to create timeless memories. Nestled among olive trees and
                        wildflowers, listen to the soft birdsong as dappled sunlight dances on the water’s surface.
                        Wander the riverbank’s smooth stones or simply pause to watch the ever‑changing play of light -
                        this tranquil Valleyside setting captures the true magic of the valley, promising moments of calm
                        and wonder you’ll cherish long after you depart.</p>
                    <a href="#" class="btn-secondary">View Our Menu</a>
                </div>
            </div>
        </div>
    </section>


    <section class="amenities-section" id="amenities">
      <div class="container">
        <h2 class="section-title">Our Amenities</h2>
        <div class="amenities-grid">
            <div class="amenity-item">
                <img class="amenity-icon" src="assets/Free-wifi-hotel.png" alt="Free High-Speed WiFi icon">
                <span class="amenity-label">Free WiFi</span>
            </div>
            <div class="amenity-item">
                <img class="amenity-icon" src="assets/spa-icon.png" alt="Spa & Hammam icon">
                <span class="amenity-label">Spa & Hammam</span>
            </div>
            <div class="amenity-item">
                <img class="amenity-icon" src="assets\kasbah-atfel-Swimming-pool.png" alt="Swimming Pool icon">
                <span class="amenity-label">Swimming Pool</span>
            </div>
            <div class="amenity-item">
                <img class="amenity-icon" src="assets/Air-conditionning hotel.png" alt="Air Conditioning icon">
                <span class="amenity-label">Air Conditioning</span>
            </div>
            <div class="amenity-item">
                <img class="amenity-icon" src="assets/Hotel-with-private-bathroom.png" alt="Restaurant icon">
                <span class="amenity-label">Private Bathroom</span>
            </div>
            <div class="amenity-item">
                <img class="amenity-icon" src="assets/Airoport-shuttle-gotel-kasbah-ourika-marakeesh.png" alt="Airport Shuttle icon">
                <span class="amenity-label">Airport Shuttle</span>
            </div>
        </div>
      </div>
    </section>


    <section class="rooms-preview" id="rooms">
        <div class="container">
            <h2 class="section-title">Our Accommodations</h2>
            <div class="filter-controls">
                <button class="filter-btn active" data-filter="all">All</button>
                <button class="filter-btn" data-filter="room">Rooms</button>
                <button class="filter-btn" data-filter="suite">Suites</button>
            </div>
            <div class="rooms-grid">
                <!-- Suite Card 1 -->
                <div class="room-card" data-category="suite">
                    <img src="Suites\TIFAOUT-Suite\affordable-hotel-Kasbah-atfel-ourika-in-morocco-summer-poolswimming2.jpg"
                        alt="Atlas Suite" class="room-image">
                    <div class="room-info">
                        <h3>TIFAOUT Suite</h3>
                        <p>Spacious suite with a private balcony overlooking the Atlas Mountains.</p>
                        <div class="room-features">
                            <span class="room-feature">King Bed</span>
                            <span class="room-feature">Mountain View</span>
                            <span class="room-feature">Private Balcony</span>
                        </div>
                        <div class="room-action-area">
                            <div class="room-price">
                                <span class="price-prefix">From</span>
                                $139/night
                            </div>
                            <a href="TIFAOUT-Suite.html" class="btn-primary">View Suite</a>
                        </div>
                    </div>
                </div>
                <!-- Suite Card 2 -->
                <div class="room-card" data-category="suite">
                    <img src="Suites/TAFOUKT-Suite/affordable-travel-hotel-Kasbah-atfel-ourika-in-morocco-summer-poolswimming.jpg"
                        alt="Atlas Suite" class="room-image">
                    <div class="room-info">
                        <h3>TAFOUKT Suite</h3>
                        <p>Luxurious suite with panoramic mountain views and traditional Moroccan décor.</p>
                        <div class="room-features">
                            <span class="room-feature">King Bed</span>
                            <span class="room-feature">Mountain View</span>
                            <span class="room-feature">Fireplace</span>
                        </div>
                        <div class="room-action-area">
                            <div class="room-price">
                                <span class="price-prefix">From</span>
                                $139/night
                            </div>
                            <a href="TAFOUKT-Suite.html" class="btn-primary">View Suite</a>
                        </div>
                    </div>
                </div>
                <!-- Suite Card 3 -->
                <div class="room-card" data-category="suite">
                    <img src="assets/image copy 2.png" alt="Atlas Suite" class="room-image">
                    <div class="room-info">
                        <h3>AYOUR Suite</h3>
                        <p>Our most opulent suite, featuring a large terrace and separate living area.</p>
                        <div class="room-features">
                            <span class="room-feature">King Bed</span>
                            <span class="room-feature">Pool View</span>
                            <span class="room-feature">Large Terrace</span>
                        </div>
                        <div class="room-action-area">
                            <div class="room-price">
                                <span class="price-prefix">From</span>
                                $139/night
                            </div>
                            <a href="AYOUR-Suite.html" class="btn-primary">View Suite</a>
                        </div>
                    </div>
                </div>

                <div class="room-card" data-category="suite">
                    <img src="Suites/ADRAR-Suite/affordable-travel-hotel-Kasbah-atfel-ourika-in-morocco-summer-Mounatinview-hotel2.jpg"
                        alt="Atlas Suite" class="room-image">
                    <div class="room-info">
                        <h3>ADRAR Suite</h3>
                        <p>Our most opulent suite, featuring a large terrace and separate living area.</p>
                        <div class="room-features">
                            <span class="room-feature">King Bed</span>
                            <span class="room-feature">Pool View</span>
                            <span class="room-feature">Large Terrace</span>
                        </div>
                        <div class="room-action-area">
                            <div class="room-price">
                                <span class="price-prefix">From</span>
                                $139/night
                            </div>
                            <a href="ADRAR-Suite.html" class="btn-primary">View Suite</a>
                        </div>
                    </div>
                </div>

                <div class="room-card" data-category="suite">
                    <img src="Suites/TASLIT-Suite/Best-luxury-travel-hotel-Kasbah-atfel-ourika-in-morocco-summer-Mounatinview-hotel1.jpg"
                        alt="Atlas Suite" class="room-image">
                    <div class="room-info">
                        <h3>TASLIT Suite</h3>
                        <p>Our most opulent suite, featuring a large terrace and separate living area.</p>
                        <div class="room-features">
                            <span class="room-feature">King Bed</span>
                            <span class="room-feature">Pool View</span>
                            <span class="room-feature">Large Terrace</span>
                        </div>
                        <div class="room-action-area">
                            <div class="room-price">
                                <span class="price-prefix">From</span>
                                $139/night
                            </div>
                            <a href="TASLIT-Suite.html" class="btn-primary">View Suite</a>
                        </div>
                    </div>
                </div>

                <div class="room-card" data-category="suite">
                    <img src="Suites/IGRANE-Suite/Best-luxury-travel-hotel-Kasbah-atfel-ourika-in-morocco-summer-Mounatinview-hotel3.jpg"
                        alt="Atlas Suite" class="room-image">
                    <div class="room-info">
                        <h3>IGRANE Suite</h3>
                        <p>Our most opulent suite, featuring a large terrace and separate living area.</p>
                        <div class="room-features">
                            <span class="room-feature">King Bed</span>
                            <span class="room-feature">Pool View</span>
                            <span class="room-feature">Large Terrace</span>
                        </div>
                        <div class="room-action-area">
                            <div class="room-price">
                                <span class="price-prefix">From</span>
                                $139/night
                            </div>
                            <a href="IGRANE-Suite.html" class="btn-primary">View Suite</a>
                        </div>
                    </div>
                </div>
                <!-- Room Card 1 -->
                <div class="room-card" data-category="room">
                    <img src="Rooms/ITHRANE-Room/Ourika-Valley-Kasbah-Atfel-hotel-with-terrace-trekking-spa-and-traditional-cuisine-near-Marrakech-budget-hotel-with-panoramic-views11.jpg"
                        alt="Berber Room" class="room-image">
                    <div class="room-info">
                        <h3>ITHRANE Room</h3>
                        <p>Cozy room featuring authentic Berber textiles and garden views.</p>
                        <div class="room-features">
                            <span class="room-feature">Queen Bed</span>
                            <span class="room-feature">Garden View</span>
                            <span class="room-feature">AC</span>
                        </div>
                        <div class="room-action-area">
                            <div class="room-price">
                                <span class="price-prefix">From</span>
                                $116/night
                            </div>
                            <a href="ITHRANE-Room.html" class="btn-primary">View Room</a>
                        </div>
                    </div>
                </div>
                <!-- Room Card 2 -->
                <div class="room-card" data-category="room">
                    <img src="Rooms/EGUENWANE-Room/Ourika-Valley-Kasbah-Atfel-hotel-with-terrace-trekking-spa-and-traditional-cuisine-near-Marrakech-budget-hotel-with-panoramic-views3.jpg"
                        alt="Ourika Room" class="room-image">
                    <div class="room-info">
                        <h3>EGUENWANE Room</h3>
                        <p>A charming room with direct access to the courtyard pool.</p>
                        <div class="room-features">
                            <span class="room-feature">Queen Bed</span>
                            <span class="room-feature">Pool View</span>
                            <span class="room-feature">Terrace Access</span>
                        </div>
                        <div class="room-action-area">
                            <div class="room-price">
                                <span class="price-prefix">From</span>
                                $116/night
                            </div>
                            <a href="EGUENWANE-Room.html" class="btn-primary">View Suite</a>
                        </div>
                    </div>
                </div>
                <!-- Room Card 3 -->
                <div class="room-card" data-category="room">
                    <img src="Rooms/AKFAF-Room/Ourika-Valley-Kasbah-Atfel-hotel-with-terrace-trekking-spa-and-traditional-cuisine-near-Marrakech-budget-hotel-with-panoramic-views4.jpg"
                        alt="Mountain View Room" class="room-image">
                    <div class="room-info">
                        <h3>AKFAF Room</h3>
                        <p>Wake up to breathtaking views of the Atlas Mountains from your window.</p>
                        <div class="room-features">
                            <span class="room-feature">King Bed</span>
                            <span class="room-feature">Mountain View</span>
                            <span class="room-feature">AC</span>
                        </div>
                        <div class="room-action-area">
                            <div class="room-price">
                                <span class="price-prefix">From</span>
                                $116/night
                            </div>
                            <a href="AKFAF-Room.html" class="btn-primary">View Suite</a>
                        </div>
                    </div>
                </div>
                <!-- Room Card 4 -->
                <div class="room-card" data-category="room">
                    <img src="Rooms/AFELLA-Room/Ourika-Valley-Kasbah-Atfel-hotel-with-terrace-trekking-spa-and-traditional-cuisine-near-Marrakech-budget-hotel-with-panoramic-views.jpg"
                        alt="Family Room" class="room-image">
                    <div class="room-info">
                        <h3>AFELLA Room</h3>
                        <p>Spacious accommodation with multiple beds, ideal for families.</p>
                        <div class="room-features">
                            <span class="room-feature">1 King, 2 Single</span>
                            <span class="room-feature">Garden View</span>
                            <span class="room-feature">Connecting Door</span>
                        </div>
                        <div class="room-action-area">
                            <div class="room-price">
                                <span class="price-prefix">From</span>
                                $116/night
                            </div>
                            <a href="AFELLA-Room.html" class="btn-primary">View Suite</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="view-more-container" style="text-align: center; margin-top: 2rem;">
                <button id="view-more-btn" class="cta-button" style="display: none;">View More</button>
            </div>
        </div>
    </section>

    <!-- =========================================
       START: Custom Testimonial Slider
       ========================================= -->
    <section class="testimonial-slider-section">
        <div class="container">
            <h2 class="section-title">What Our Guests Say</h2>
        </div>

        <div class="testimonial-slider-container" id="testimonial-slider">
            <!-- Testimonial Cards will be dynamically inserted here by JavaScript -->
        </div>

        <div class="slider-nav">
            <button id="prev-btn" class="slider-btn" aria-label="Previous testimonial">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
                </svg>
            </button>
            <button id="next-btn" class="slider-btn" aria-label="Next testimonial">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                </svg>
            </button>
        </div>
    </section>


    <!-- =========================================
   START: Luxury Awards Section
   ========================================= -->
    <section class="luxury-awards-section">
        <div class="container">
            <div class="section-header-centered">
                <h2 class="section-title">Our Recognition of Excellence</h2>
                <p class="section-subtitle">
                    We are deeply honored by the recognition from our esteemed partners, a testament to the
                    unforgettable experiences we strive to create for every guest.
                </p>
            </div>

            <div class="awards-container">
                <!-- Award 1: Booking.com -->
                <div class="luxury-award-item">
                    <div class="award-image-wrapper">
                        <img src="assets\Digital-Award-TRA-2025.png" alt="Booking.com Traveller Review Award 2025">
                    </div>
                    <div class="award-details">
                        <div class="award-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                            </svg>
                        </div>
                        <h3 class="award-title">Traveller Review Award 2025</h3>
                        <p class="award-partner">Presented by Booking.com</p>
                        <p class="award-description">
                            This prestigious award celebrates our consistent dedication to hospitality, earned through
                            hundreds of exceptional reviews from our valued guests.
                        </p>
                    </div>
                </div>

                <!-- Award 2: Kayak -->
                <div class="luxury-award-item reverse-layout">
                    <div class="award-image-wrapper">
                        <img src="assets\LIGHT_MEDIUM_TRAVEL_AWARDS.png" alt="Kayak Travel Awards 2023">
                    </div>
                    <div class="award-details">
                        <div class="award-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24"
                                height="24">
                                <path
                                    d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                            </svg>
                        </div>
                        <h3 class="award-title">Top Rated Hotel Award 2023</h3>
                        <p class="award-partner">Presented by Kayak</p>
                        <p class="award-description">
                            Recognized as one of the best hotels by Kayak users, this award reflects our commitment to
                            providing a world-class and memorable stay.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- =========================================
   END: Luxury Awards Section
   ========================================= -->




    <footer id="footer">
        <!-- Paste your updated footer code here -->
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Atfel Kasbah</h3>
                    <p>Experience authentic Moroccan hospitality in the heart of Ourika Valley.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="index.html#rooms">Rooms & Suites</a></li>
                        <li><a href="gallery.html">Gallery</a></li>
                        <li><a href="aboutus.html">About Us</a></li>
                        <li><a href="blog.html">Blog / News</a></li>
                        <li><a href="faq.html">FAQ</a></li>
                        <li><a href="contactus.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <ul>
                        <li> Ourika Valley, Morocco</li>
                        <li> +212652883513</li>
                        <li><EMAIL></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms & Conditions</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>© 2025 Atfel Kasbah Hotel. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Your existing inline script -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- CONSTANTS ---
            const header = document.getElementById('header');
            const navToggle = document.querySelector('.mobile-nav-toggle');
            const primaryNav = document.getElementById('primary-navigation');
            const heroCtaButton = document.getElementById('hero-cta-button');
            const typingElement = document.getElementById('typing-subtitle');
            const filterContainer = document.querySelector('.filter-controls');
            const roomCards = document.querySelectorAll('.room-card');
            const viewMoreBtn = document.getElementById('view-more-btn');
            const initialRoomsToShow = 4;
            let roomsCurrentlyShown = initialRoomsToShow;

            // --- TYPING ANIMATION ---
            if (typingElement) {
                const textArray = [
                    "Your home away from home.",
                    "One of the best views in Morocco.",
                    "Experience authentic charm in Ourika Valley."
                ];
                let textArrayIndex = 0;
                let charIndex = 0;
                let isDeleting = false;
                const typeSpeed = 150;
                const deleteSpeed = 75;
                const delayBetweenWords = 2000;

                function type() {
                    const currentText = textArray[textArrayIndex];
                    if (isDeleting) {
                        charIndex--;
                        typingElement.textContent = currentText.substring(0, charIndex);
                    } else {
                        charIndex++;
                        typingElement.textContent = currentText.substring(0, charIndex);
                    }

                    if (!isDeleting && charIndex === currentText.length) {
                        isDeleting = true;
                        setTimeout(type, delayBetweenWords);
                    } else if (isDeleting && charIndex === 0) {
                        isDeleting = false;
                        textArrayIndex = (textArrayIndex + 1) % textArray.length;
                        setTimeout(type, 500);
                    } else {
                        setTimeout(type, isDeleting ? deleteSpeed : typeSpeed);
                    }
                }
                type();
            }

            // --- MOBILE NAVIGATION ---
            if (navToggle && primaryNav) {
                navToggle.addEventListener('click', () => {
                    const isVisible = primaryNav.getAttribute('data-visible') === 'true';
                    primaryNav.setAttribute('data-visible', !isVisible);
                    navToggle.setAttribute('aria-expanded', !isVisible);
                });
            }

            // --- HEADER SCROLL BEHAVIOR ---
            const handleScroll = () => {
                if (window.scrollY > 50) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }

                if (!heroCtaButton) return;
                const heroButtonRect = heroCtaButton.getBoundingClientRect();
                const headerHeight = header.offsetHeight;

                if (heroButtonRect.bottom < headerHeight) {
                    header.classList.add('header-cta-visible');
                } else {
                    header.classList.remove('header-cta-visible');
                }
            };
            window.addEventListener('scroll', handleScroll);

            // --- SMOOTH SCROLL FOR ANCHOR LINKS ---
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    const target = document.querySelector(targetId);
                    if (target) {
                        target.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        if (primaryNav.getAttribute('data-visible') === 'true') {
                            primaryNav.setAttribute('data-visible', false);
                            navToggle.setAttribute('aria-expanded', false);
                        }
                    }
                });
            });

            // --- ROOMS FILTERING AND 'VIEW MORE' LOGIC ---
            const getVisibleCards = () => {
                const filterValue = filterContainer.querySelector('.active').getAttribute('data-filter');
                const filteredCards = [];
                roomCards.forEach(card => {
                    if (filterValue === 'all' || card.dataset.category === filterValue) {
                        filteredCards.push(card);
                    }
                });
                return filteredCards;
            };

            const updateRoomVisibility = () => {
                const visibleCards = getVisibleCards();

                // Hide all cards first to reset the view
                roomCards.forEach(card => card.style.display = 'none');

                // Show only the cards that should be visible
                visibleCards.forEach((card, index) => {
                    if (index < roomsCurrentlyShown) {
                        card.style.display = 'block';
                    }
                });

                // Logic for showing/hiding the "View More" button
                if (visibleCards.length > roomsCurrentlyShown) {
                    viewMoreBtn.style.display = 'inline-block';
                } else {
                    viewMoreBtn.style.display = 'none';
                }
            };

            if (filterContainer) {
                filterContainer.addEventListener('click', (e) => {
                    // Only run if a non-active filter button is clicked
                    if (e.target.matches('.filter-btn:not(.active)')) {
                        filterContainer.querySelector('.active').classList.remove('active');
                        e.target.classList.add('active');
                        // Reset the number of shown rooms to the initial count
                        roomsCurrentlyShown = initialRoomsToShow;
                        updateRoomVisibility();
                    }
                });
            }

            if (viewMoreBtn) {
                viewMoreBtn.addEventListener('click', () => {
                    // Set the number to show to the total number of available cards
                    roomsCurrentlyShown = getVisibleCards().length;
                    updateRoomVisibility();
                });
            }

            // Initial call to set up the rooms section correctly on page load
            updateRoomVisibility();
        });
    </script>

    <!-- Link to the JavaScript files -->
    <script src="assets/js/main.js" defer></script>
    <script src="hamburger-menu-fix.js" defer></script>
    <script src="assets/js/language-switcher.js" defer></script>

</body>

</html>