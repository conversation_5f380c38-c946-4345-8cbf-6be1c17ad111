<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us | Atfel Kasbah Hotel, Ourika Valley</title>
    <meta name="description" content="Get in touch with Atfel Kasbah. Contact us for reservations, questions, or special requests. We look forward to welcoming you.">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="hamburger-menu-fix.css">
    <link rel="stylesheet" href="assets/css/language-switcher.css">
    
    <!-- Fallback for when JavaScript is disabled -->
    <noscript>
        <style>
            .mobile-nav-toggle { display: none; }
            .nav-links { 
                display: block !important; 
                position: static !important;
                transform: none !important;
                background: transparent !important;
                width: 100% !important;
                pointer-events: auto !important;
            }
        </style>
    </noscript>
    <style>
        :root {
            --primary-color: #8B4513; /* SaddleBrown */
            --secondary-color: #f7f5f2;
            --text-dark: #2a2a2a;
            --text-light: #6b6b6b;
            --white: #ffffff;
            --border-color: #e5e0da;
            --success-color: #28a745;
        }

        /* --- Contact Page Specific Styles --- */
        .contact-section {
            display: flex;
            min-height: calc(100vh - 80px); /* 80px is approx header height */
            align-items: stretch;
            padding-top: 80px; /* Header offset */
        }

        .contact-info-wrapper {
            background-color: var(--white);
            flex: 1;
            padding: 4rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .contact-image-wrapper {
            flex: 1;
            background-image: url('assets/image.png'); /* Replace with a high-quality image */
            background-size: cover;
            background-position: center;
        }
        
        .contact-info-wrapper h1 {
            font-size: 3rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 1rem;
        }

        .contact-info-wrapper .subtitle {
            font-size: 1.2rem;
            color: var(--text-light);
            margin-bottom: 3rem;
        }

        /* Form Styles */
        .contact-form .form-group {
            position: relative;
            margin-bottom: 2rem;
        }

        .contact-form .form-control {
            width: 100%;
            border: none;
            border-bottom: 2px solid var(--border-color);
            padding: 10px 0;
            background-color: transparent;
            font-size: 1rem;
            color: var(--text-dark);
            transition: border-color 0.3s ease;
        }

        .contact-form .form-control:focus {
            outline: none;
            border-bottom-color: var(--primary-color);
        }

        .contact-form .form-label {
            position: absolute;
            top: 10px;
            left: 0;
            font-size: 1rem;
            color: var(--text-light);
            pointer-events: none;
            transition: all 0.3s ease;
        }

        .contact-form .form-control:focus + .form-label,
        .contact-form .form-control:not(:placeholder-shown) + .form-label {
            top: -15px;
            font-size: 0.85rem;
            color: var(--primary-color);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 100px;
        }

        .btn-submit {
            display: inline-block;
            background-color: var(--primary-color);
            color: var(--white);
            padding: 15px 40px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            border: 2px solid var(--primary-color);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-submit:hover {
            background-color: transparent;
            color: var(--primary-color);
        }

        /* Direct Contact Info */
        .direct-contact {
            margin-top: 4rem;
            border-top: 1px solid var(--border-color);
            padding-top: 2rem;
        }

        .direct-contact .contact-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
            color: var(--text-light);
        }
        
        .direct-contact .contact-item a {
            color: var(--text-light);
            text-decoration: none;
            transition: color 0.3s;
        }

        .direct-contact .contact-item a:hover {
            color: var(--primary-color);
        }

        .direct-contact .icon {
            color: var(--primary-color);
            font-size: 1.5rem;
        }
        
        /* Responsive */
        @media (max-width: 992px) {
            .contact-section {
                flex-direction: column;
                padding-top: 80px;
            }
            .contact-info-wrapper {
                padding: 3rem;
            }
            .contact-image-wrapper {
                min-height: 400px;
                order: -1; /* Puts image on top */
            }
        }
    </style>
</head>
<body>

    <header id="header">
        <!-- Re-using the same header for consistency -->
        <nav class="container">
            <a href="index.html" class="logo">Atfel Kasbah</a>
            <button class="mobile-nav-toggle" aria-controls="primary-navigation" aria-expanded="false">
                <span class="line line-top"></span><span class="line line-middle"></span><span class="line line-bottom"></span>
            </button>
            <ul id="primary-navigation" data-visible="false" class="nav-links">
                <li><a href="index.html#home">Home</a></li>
                <li><a href="index.html#rooms">Rooms</a></li>
                <li><a href="gallery.html">Gallery</a></li>
                <li class="cta-item"><a href="index.html#rooms" class="cta-button">Book now</a></li>
                <li><a href="aboutus.html">About Us</a></li>
                <li><a href="contactus.html">Contact</a></li>

                <!-- Language Switcher -->
                <li class="language-switcher">
                    <div class="language-dropdown">
                        <button class="language-btn" aria-label="Switch language">
                            <span class="flag-icon flag-en"></span>
                            <span class="language-text">EN</span>
                        </button>
                        <div class="language-menu">
                            <a href="contactus.html" class="language-option active" data-lang="en">
                                <span class="flag-icon flag-en"></span>
                                <span>English</span>
                            </a>
                            <a href="contactus-fr.html" class="language-option" data-lang="fr">
                                <span class="flag-icon flag-fr"></span>
                                <span>Français</span>
                            </a>
                        </div>
                    </div>
                </li>
            </ul>
        </nav>
    </header>

    <main>
        <section class="contact-section">
            <div class="contact-info-wrapper">
                <h1>Get In Touch</h1>
                <p class="subtitle">Have a question or a special request? Fill out the form or contact us directly.</p>
                
                <form action="#" class="contact-form">
                    <div class="form-group">
                        <input type="text" id="name" name="name" class="form-control" placeholder=" " required>
                        <label for="name" class="form-label">Full Name</label>
                    </div>
                    <div class="form-group">
                        <input type="email" id="email" name="email" class="form-control" placeholder=" " required>
                        <label for="email" class="form-label">Email Address</label>
                    </div>
                    <div class="form-group">
                        <textarea id="message" name="message" class="form-control" placeholder=" " required></textarea>
                        <label for="message" class="form-label">Your Message</label>
                    </div>
                    <button type="submit" class="btn-submit">Send Message</button>
                </form>

                <div class="direct-contact">
                    <div class="contact-item">
                        <span class="icon">📍</span>
                        <span>Ourika Valley, Morocco</span>
                    </div>
                    <div class="contact-item">
                        <span class="icon">✉️</span>
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                    <div class="contact-item">
                        <span class="icon">📞</span>
                        <a href="tel:+212123456789">+212 123 456 789</a>
                    </div>
                </div>
            </div>
            <div class="contact-image-wrapper">
                <!-- Background image is set in CSS -->
            </div>
        </section>
    </main>
    
    <footer id="footer">
        <!-- Paste your updated footer code here -->
        <div class="container">
            <div class="footer-content">
                <div class="footer-section"><h3>Atfel Kasbah</h3><p>Experience authentic Moroccan hospitality in the heart of Ourika Valley.</p></div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="index.html#rooms">Rooms & Suites</a></li>
                        <li><a href="gallery.html">Gallery</a></li>
                        <li><a href="aboutus.html">About Us</a></li>
                        <li><a href="blog.html">Blog / News</a></li>
                        <li><a href="faq.html">FAQ</a></li>
                        <li><a href="contactus.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <ul><li>📍 Ourika Valley, Morocco</li><li>📞 +212652883513</li><li>✉️ <EMAIL></li></ul>
                </div>
                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul><li><a href="#">Privacy Policy</a></li><li><a href="#">Terms & Conditions</a></li></ul>
                </div>
            </div>
            <div class="footer-bottom"><p>© 2025 Atfel Kasbah Hotel. All rights reserved.</p></div>
        </div>
    </footer>
    <!-- The footer can be omitted on this page for a more immersive feel, or added back in if you prefer. -->

    <script>
        // Only basic script needed is for the mobile nav
        document.addEventListener('DOMContentLoaded', () => {
            const navToggle = document.querySelector('.mobile-nav-toggle');
            const primaryNav = document.getElementById('primary-navigation');
            if (navToggle) {
                navToggle.addEventListener('click', () => {
                    const isVisible = primaryNav.getAttribute('data-visible') === 'true';
                    primaryNav.setAttribute('data-visible', !isVisible);
                    navToggle.setAttribute('aria-expanded', !isVisible);
                });
            }
        });
    </script>
    <script src="assets/js/language-switcher.js" defer></script>
</body>
</html>

