<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us | Atfel Kasbah Hotel, Ourika Valley</title>
    <meta name="description" content="Discover the story behind Atfel Kasbah, a sanctuary of peace and authentic Moroccan heritage nestled in the Ourika Valley.">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="hamburger-menu-fix.css">
    <link rel="stylesheet" href="assets/css/language-switcher.css">
    
    <!-- Fallback for when JavaScript is disabled -->
    <noscript>
        <style>
            .mobile-nav-toggle { display: none; }
            .nav-links { 
                display: block !important; 
                position: static !important;
                transform: none !important;
                background: transparent !important;
                width: 100% !important;
                pointer-events: auto !important;
            }
        </style>
    </noscript>
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #f7f5f2;
            --text-dark: #2a2a2a;
            --text-light: #6b6b6b;
            --white: #ffffff;
            --border-color: #e5e0da;
        }

        /* --- MAJOR CHANGES START HERE --- */

        /* This ensures the page content respects the header and footer */
        body {
            background-color: var(--secondary-color);
        }

        .about-page-wrapper {
            padding-top: 100px; /* Added padding to push content below the fixed header */
            padding-bottom: 60px;
        }

        /* New hero image container */
        .about-hero-image {
            height: 500px;
            background-image: url('assets/affordable-hotel-Kasbah-atfel-ourika-in-morocco27.jpg'); /* Your hero image */
            background-size: cover;
            background-position: center;
            border-radius: 12px;
            margin: 0 auto;
            max-width: 1200px; /* Limits width on very large screens */
            
            /* The corrected wave effect at the BOTTOM of the image */
            clip-path: ellipse(85% 100% at 50% 100%);
        }

        /* The story content box */
        .about-story-box {
            max-width: 850px;
            margin: 0 auto;
            background-color: var(--white);
            padding: 3rem 4rem;
            border-radius: 12px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);

            /* This pulls the story box up to overlap the image */
            margin-top: -120px; 
            
            position: relative; /* Ensures it sits on top of the image */
            z-index: 2;
            text-align: center;
        }
        
        .about-story-box h1 {
            font-size: 3rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 1rem;
        }

        .about-story-box .subtitle {
            font-size: 1.25rem;
            color: var(--primary-color);
            margin-bottom: 2.5rem;
        }

        .about-story-box p {
            font-size: 1.1rem;
            line-height: 1.8;
            color: var(--text-light);
            margin-bottom: 1.5rem;
            text-align: justify; /* Justified text for a clean, classic look */
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .about-hero-image {
                height: 350px;
                border-radius: 0; /* Full width on mobile */
            }
            .about-story-box {
                padding: 2rem;
                margin-top: -80px;
            }
            .about-story-box h1 {
                font-size: 2.5rem;
            }
        }
        
    </style>
</head>
<body>

    <header id="header">
        <!-- The header is now guaranteed to be visible and functional -->
        <nav class="container">
            <a href="index.html" class="logo">Atfel Kasbah</a>
            <button class="mobile-nav-toggle" aria-controls="primary-navigation" aria-expanded="false">
                <span class="line line-top"></span><span class="line line-middle"></span><span class="line line-bottom"></span>
            </button>
            <ul id="primary-navigation" data-visible="false" class="nav-links">
                <li><a href="index.html#rooms">Rooms</a></li>
                <li><a href="gallery.html">Gallery</a></li>
                <li class="cta-item"><a href="index.html#rooms" class="cta-button">Book now</a></li>
                <li><a href="aboutus.html">About Us</a></li>
                <li><a href="contactus.html">Contact</a></li>

                <!-- Language Switcher -->
                <li class="language-switcher">
                    <div class="language-dropdown">
                        <button class="language-btn" aria-label="Switch language">
                            <span class="flag-icon flag-en"></span>
                            <span class="language-text">EN</span>
                        </button>
                        <div class="language-menu">
                            <a href="aboutus.html" class="language-option active" data-lang="en">
                                <span class="flag-icon flag-en"></span>
                                <span>English</span>
                            </a>
                            <a href="aboutus-fr.html" class="language-option" data-lang="fr">
                                <span class="flag-icon flag-fr"></span>
                                <span>Français</span>
                            </a>
                        </div>
                    </div>
                </li>
            </ul>
        </nav>
    </header>

    <main>
        <div class="about-page-wrapper">
            <!-- This container holds the image and allows it to be placed correctly on the page -->
            <div class="about-hero-image"></div>

            <!-- This is the text content that floats over the bottom of the image -->
            <div class="about-story-box">
                <h1>The Heart of the Ourika Valley</h1>
                <p class="subtitle">A Story of Heritage, Hospitality, and Harmony</p>
                <p><strong>Kasbah Atfel</strong> sits just 30 km south of Marrakech in the heart of the <strong>Ourika Valley</strong>, a lush mountain refuge renowned for its <strong>Seven Waterfalls</strong> and traditional <strong>Berber villages</strong>. Nestled among olive groves and wildflower–dotted hillsides, our kasbah blends classic Moroccan architecture with modern comforts to create an authentic yet luxurious retreat </p>

                <p>Step inside and discover a world of <strong>wellness</strong> and relaxation: unwind in our restored <strong>hammam</strong>, soak in the <strong>heated Jacuzzi</strong>, or choose from an extensive menu of <strong>massages</strong> and <strong>spa wellness packages</strong> - all set within candlelit stone walls and verdant gardens</p>

                <p>Beyond our gates, the valley offers endless <strong>outdoor adventures</strong>: hike scenic trails to hidden cascades, visit colorful weekly souks, explore botanical wonders at Anima Garden, or learn Berber pottery and cooking in nearby villages. Our expert local guides can tailor excursions to your interests, ensuring you experience the very best of <strong>Ourika Valley culture</strong> and landscape</p>

                <p>Back at the kasbah, enjoy our <strong>outdoor swimming pool</strong> framed by Atlas foothills, indulge in farm‑to‑table Moroccan cuisine at our restaurant, or simply relax on sun‑drenched terraces. With free Wi‑Fi, airport transfers, family‑friendly suites and personalized service, <strong>Kasbah Atfel</strong> is your gateway to an unforgettable Moroccan escape.</p>

            </div>
        </div>
    </main>
    <!-- =========================================
       START: Custom Testimonial Slider
       ========================================= -->
    <section class="testimonial-slider-section">
        <div class="container">
            <h2 class="section-title">What Our Guests Say</h2>
        </div>

        <div class="testimonial-slider-container" id="testimonial-slider">
            <!-- Testimonial Cards will be dynamically inserted here by JavaScript -->
        </div>

        <div class="slider-nav">
            <button id="prev-btn" class="slider-btn" aria-label="Previous testimonial">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
                </svg>
            </button>
            <button id="next-btn" class="slider-btn" aria-label="Next testimonial">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                </svg>
            </button>
        </div>
    </section>
    <!-- =========================================
       END: Custom Testimonial Slider
       ========================================= -->

    <footer id="footer">
        <!-- Paste your updated footer code here -->
        <div class="container">
            <div class="footer-content">
                <div class="footer-section"><h3>Atfel Kasbah</h3><p>Experience authentic Moroccan hospitality in the heart of Ourika Valley.</p></div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="index.html#rooms">Rooms & Suites</a></li>
                        <li><a href="gallery.html">Gallery</a></li>
                        <li><a href="aboutus.html">About Us</a></li>
                        <li><a href="blog.html">Blog / News</a></li>
                        <li><a href="faq.html">FAQ</a></li>
                        <li><a href="contactus.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <ul><li>📍 Ourika Valley, Morocco</li><li>📞 +212652883513</li><li>✉️ <EMAIL></li></ul>
                </div>
                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul><li><a href="#">Privacy Policy</a></li><li><a href="#">Terms & Conditions</a></li></ul>
                </div>
            </div>
            <div class="footer-bottom"><p>© 2025 Atfel Kasbah Hotel. All rights reserved.</p></div>
        </div>
    </footer>
    
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const navToggle = document.querySelector('.mobile-nav-toggle');
            const primaryNav = document.getElementById('primary-navigation');
            if (navToggle) {
                navToggle.addEventListener('click', () => {
                    const isVisible = primaryNav.getAttribute('data-visible') === 'true';
                    primaryNav.setAttribute('data-visible', !isVisible);
                    navToggle.setAttribute('aria-expanded', !isVisible);
                });
            }
        });
    </script>
    <script src="assets/js/main.js" defer></script>
    <script src="hamburger-menu-fix.js" defer></script>
    <script src="assets/js/language-switcher.js" defer></script>
</body>
</html>
