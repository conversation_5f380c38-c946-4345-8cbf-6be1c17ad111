<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contactez-Nous | Hôtel Kasbah Atfel, Vallée d'Ourika</title>
    <meta name="description" content="Contactez Kasbah Atfel. Contactez-nous pour des réservations, des questions ou des demandes spéciales. Nous avons hâte de vous accueillir.">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="hamburger-menu-fix.css">
    <link rel="stylesheet" href="assets/css/language-switcher.css">
    
    <!-- Fallback for when JavaScript is disabled -->
    <noscript>
        <style>
            .mobile-nav-toggle { display: none; }
            .nav-links { 
                display: block !important; 
                position: static !important;
                transform: none !important;
                background: transparent !important;
                width: 100% !important;
                pointer-events: auto !important;
            }
        </style>
    </noscript>
    <style>
        :root {
            --primary-color: #8B4513; /* SaddleBrown */
            --secondary-color: #f7f5f2;
            --text-dark: #2a2a2a;
            --text-light: #6b6b6b;
            --white: #ffffff;
            --border-color: #e5e0da;
            --success-color: #28a745;
        }

        /* --- Contact Page Specific Styles --- */
        .contact-section {
            display: flex;
            min-height: calc(100vh - 80px); /* 80px is approx header height */
            align-items: stretch;
            padding-top: 80px; /* Header offset */
        }

        .contact-info-wrapper {
            background-color: var(--white);
            flex: 1;
            padding: 4rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .contact-form-wrapper {
            background-color: var(--secondary-color);
            flex: 1;
            padding: 4rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .contact-info h1 {
            font-size: 3rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 1rem;
        }

        .contact-info .subtitle {
            font-size: 1.25rem;
            color: var(--primary-color);
            margin-bottom: 2rem;
        }

        .contact-info p {
            font-size: 1.1rem;
            line-height: 1.8;
            color: var(--text-light);
            margin-bottom: 2rem;
        }

        /* Contact Form Styles */
        .contact-form {
            max-width: 500px;
        }

        .contact-form h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 2rem;
        }

        .form-group {
            position: relative;
            margin-bottom: 2rem;
        }

        .contact-form .form-control {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 1rem;
            background-color: var(--white);
            transition: all 0.3s ease;
        }

        .contact-form .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
        }

        .form-label {
            position: absolute;
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
            font-size: 1rem;
            color: var(--text-light);
            pointer-events: none;
            transition: all 0.3s ease;
        }

        .contact-form .form-control:focus + .form-label,
        .contact-form .form-control:not(:placeholder-shown) + .form-label {
            top: -15px;
            font-size: 0.85rem;
            color: var(--primary-color);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 100px;
        }

        .btn-submit {
            display: inline-block;
            background-color: var(--primary-color);
            color: var(--white);
            padding: 15px 40px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            border: 2px solid var(--primary-color);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-submit:hover {
            background-color: transparent;
            color: var(--primary-color);
        }

        /* Direct Contact Info */
        .direct-contact {
            margin-top: 4rem;
            border-top: 1px solid var(--border-color);
            padding-top: 2rem;
        }

        .direct-contact .contact-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
            color: var(--text-light);
        }

        .direct-contact .contact-item .icon {
            width: 24px;
            height: 24px;
            color: var(--primary-color);
        }

        /* Success Message */
        .success-message {
            display: none;
            background-color: var(--success-color);
            color: var(--white);
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .contact-section {
                flex-direction: column;
                padding-top: 100px;
            }

            .contact-info-wrapper,
            .contact-form-wrapper {
                padding: 2rem;
            }

            .contact-info h1 {
                font-size: 2.5rem;
            }

            .contact-form h2 {
                font-size: 2rem;
            }
        }

        @media (max-width: 480px) {
            .contact-info-wrapper,
            .contact-form-wrapper {
                padding: 1.5rem;
            }

            .contact-info h1 {
                font-size: 2rem;
            }

            .contact-form h2 {
                font-size: 1.8rem;
            }
        }
    </style>
</head>

<body>
    <header id="header">
        <nav class="container">
            <a href="index-fr.html" class="logo">Atfel Kasbah</a>

            <button class="mobile-nav-toggle" aria-controls="primary-navigation" aria-expanded="false">
                <span class="line line-top"></span>
                <span class="line line-middle"></span>
                <span class="line line-bottom"></span>
            </button>

            <ul id="primary-navigation" data-visible="false" class="nav-links">
                <li><a href="index-fr.html#home">Accueil</a></li>
                <li><a href="index-fr.html#rooms">Chambres</a></li>
                <li><a href="gallery-fr.html">Galerie</a></li>
                <li class="cta-item">
                    <a href="index-fr.html#rooms" id="header-cta-button" class="cta-button">Réserver</a>
                </li>
                <li><a href="aboutus-fr.html">À Propos</a></li>
                <li><a href="contactus-fr.html">Contact</a></li>
                
                <!-- Language Switcher -->
                <li class="language-switcher">
                    <div class="language-dropdown">
                        <button class="language-btn" aria-label="Changer de langue">
                            <span class="flag-icon flag-fr"></span>
                            <span class="language-text">FR</span>
                        </button>
                        <div class="language-menu">
                            <a href="contactus.html" class="language-option" data-lang="en">
                                <span class="flag-icon flag-en"></span>
                                <span>English</span>
                            </a>
                            <a href="contactus-fr.html" class="language-option active" data-lang="fr">
                                <span class="flag-icon flag-fr"></span>
                                <span>Français</span>
                            </a>
                        </div>
                    </div>
                </li>
            </ul>
        </nav>
    </header>

    <main class="contact-section">
        <!-- Contact Information Side -->
        <div class="contact-info-wrapper">
            <div class="contact-info">
                <h1>Contactez-Nous</h1>
                <p class="subtitle">Nous sommes là pour vous aider</p>
                <p>
                    Que vous planifiez votre séjour parfait, ayez des questions sur nos équipements, ou souhaitiez organiser une expérience spéciale, notre équipe dévouée est prête à vous assister. Contactez-nous et laissez-nous créer des souvenirs inoubliables ensemble.
                </p>

                <div class="direct-contact">
                    <div class="contact-item">
                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <div>
                            <strong>Adresse</strong><br>
                            Vallée d'Ourika, Maroc
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        <div>
                            <strong>Téléphone</strong><br>
                            +212652883513
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        <div>
                            <strong>Email</strong><br>
                            <EMAIL>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Form Side -->
        <div class="contact-form-wrapper">
            <form class="contact-form" id="contactForm">
                <h2>Envoyez-nous un Message</h2>
                
                <div class="success-message" id="successMessage">
                    Merci ! Votre message a été envoyé avec succès. Nous vous répondrons bientôt.
                </div>

                <div class="form-group">
                    <input type="text" class="form-control" id="name" name="name" placeholder=" " required>
                    <label for="name" class="form-label">Nom Complet</label>
                </div>

                <div class="form-group">
                    <input type="email" class="form-control" id="email" name="email" placeholder=" " required>
                    <label for="email" class="form-label">Adresse Email</label>
                </div>

                <div class="form-group">
                    <input type="tel" class="form-control" id="phone" name="phone" placeholder=" ">
                    <label for="phone" class="form-label">Numéro de Téléphone</label>
                </div>

                <div class="form-group">
                    <textarea class="form-control" id="message" name="message" placeholder=" " required></textarea>
                    <label for="message" class="form-label">Votre Message</label>
                </div>

                <button type="submit" class="btn-submit">Envoyer le Message</button>
            </form>
        </div>
    </main>

    <footer id="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Atfel Kasbah</h3>
                    <p>Découvrez l'hospitalité marocaine authentique au cœur de la vallée d'Ourika.</p>
                </div>
                <div class="footer-section">
                    <h3>Liens Rapides</h3>
                    <ul>
                        <li><a href="index-fr.html">Accueil</a></li>
                        <li><a href="index-fr.html#rooms">Chambres & Suites</a></li>
                        <li><a href="gallery-fr.html">Galerie</a></li>
                        <li><a href="aboutus-fr.html">À Propos</a></li>
                        <li><a href="blog-fr.html">Blog / Actualités</a></li>
                        <li><a href="faq-fr.html">FAQ</a></li>
                        <li><a href="contactus-fr.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Informations de Contact</h3>
                    <ul>
                        <li>📍 Vallée d'Ourika, Maroc</li>
                        <li>📞 +212652883513</li>
                        <li>✉️ <EMAIL></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Légal</h3>
                    <ul>
                        <li><a href="#">Politique de Confidentialité</a></li>
                        <li><a href="#">Conditions Générales</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>© 2025 Hôtel Kasbah Atfel. Tous droits réservés.</p>
            </div>
        </div>
    </footer>

    <script src="hamburger-menu-fix.js" defer></script>
    <script>
        // Simple form submission handler
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show success message
            document.getElementById('successMessage').style.display = 'block';
            
            // Reset form
            this.reset();
            
            // Hide success message after 5 seconds
            setTimeout(() => {
                document.getElementById('successMessage').style.display = 'none';
            }, 5000);
        });
    </script>
    
    <script src="hamburger-menu-fix.js" defer></script>
    <script src="assets/js/language-switcher.js" defer></script>

</body>
</html>
